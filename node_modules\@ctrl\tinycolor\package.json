{"_from": "@ctrl/tinycolor@^3.4.1", "_id": "@ctrl/tinycolor@3.6.1", "_inBundle": false, "_integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==", "_location": "/@ctrl/tinycolor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@ctrl/tinycolor@^3.4.1", "name": "@ctrl/tinycolor", "escapedName": "@ctrl%2ftinycolor", "scope": "@ctrl", "rawSpec": "^3.4.1", "saveSpec": null, "fetchSpec": "^3.4.1"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "_shasum": "b6c75a56a1947cc916ea058772d666a2c8932f31", "_spec": "@ctrl/tinycolor@^3.4.1", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "bugs": {"url": "https://github.com/scttcper/tinycolor/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Fast, small color manipulation and conversion for JavaScript", "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "engines": {"node": ">=10"}, "files": ["dist"], "homepage": "https://tinycolor.vercel.app", "jest": {"testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"(.+)\\.js": "$1"}}, "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "license": "MIT", "main": "dist/public_api.js", "module": "dist/module/public_api.js", "name": "@ctrl/tinycolor", "publishConfig": {"access": "public"}, "release": {"branch": "master"}, "repository": {"type": "git", "url": "git+https://github.com/scttcper/tinycolor.git"}, "scripts": {"build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:demo": "rollup -c rollup.demo.js", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch", "watch:demo": "rollup -c rollup.demo.js -w"}, "sideEffects": false, "typings": "dist/public_api.d.ts", "version": "3.6.1"}