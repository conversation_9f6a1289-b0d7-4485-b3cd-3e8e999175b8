{"_from": "lodash-es@^4.17.21", "_id": "lodash-es@4.17.21", "_inBundle": false, "_integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==", "_location": "/lodash-es", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash-es@^4.17.21", "name": "lodash-es", "escapedName": "lodash-es", "rawSpec": "^4.17.21", "saveSpec": null, "fetchSpec": "^4.17.21"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "_shasum": "43e626c46e6591b7750beb2b50117390c609e3ee", "_spec": "lodash-es@^4.17.21", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Lodash exported as ES modules.", "homepage": "https://lodash.com/custom-builds", "jsnext:main": "lodash.js", "keywords": ["es6", "modules", "stdlib", "util"], "license": "MIT", "main": "lodash.js", "module": "lodash.js", "name": "lodash-es", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "sideEffects": false, "type": "module", "version": "4.17.21"}