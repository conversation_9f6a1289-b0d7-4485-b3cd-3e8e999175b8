{"_from": "@esbuild/linux-x64@0.21.5", "_id": "@esbuild/linux-x64@0.21.5", "_inBundle": false, "_integrity": "sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==", "_location": "/@esbuild/linux-x64", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@esbuild/linux-x64@0.21.5", "name": "@esbuild/linux-x64", "escapedName": "@esbuild%2flinux-x64", "scope": "@esbuild", "rawSpec": "0.21.5", "saveSpec": null, "fetchSpec": "0.21.5"}, "_requiredBy": ["/esbuild"], "_resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz", "_shasum": "6d8f0c768e070e64309af8004bb94e68ab2bb3b0", "_spec": "@esbuild/linux-x64@0.21.5", "_where": "/mnt/e/www/demo1/node_modules/esbuild", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "The Linux 64-bit binary for esbuild, a JavaScript bundler.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "name": "@esbuild/linux-x64", "os": ["linux"], "preferUnplugged": true, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "version": "0.21.5"}