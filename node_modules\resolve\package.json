{"_from": "resolve@^1.22.2", "_id": "resolve@1.22.10", "_inBundle": false, "_integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "_location": "/resolve", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve@^1.22.2", "name": "resolve", "escapedName": "resolve", "rawSpec": "^1.22.2", "saveSpec": null, "fetchSpec": "^1.22.2"}, "_requiredBy": ["/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "_shasum": "b663e83ffb09bbf2386944736baae803029b8b39", "_spec": "resolve@^1.22.2", "_where": "/mnt/e/www/demo1/node_modules/unplugin-vue-components", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"resolve": "bin/resolve"}, "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bundleDependencies": false, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "deprecated": false, "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "array.prototype.map": "^1.0.7", "copy-dir": "^1.3.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "mv": "^2.1.1", "npmignore": "^0.3.1", "object-keys": "^1.1.1", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tap": "0.4.13", "tape": "^5.9.0", "tmp": "^0.0.31"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/browserify/resolve#readme", "keywords": ["resolve", "require", "node", "module"], "license": "MIT", "main": "index.js", "name": "resolve", "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json", "test/list-exports"]}, "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "posttest": "npm run test:multirepo && npx npm@'>= 10.2' audit --production", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test": "npm run --silent tests-only", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test", "tests-only": "tape test/*.js"}, "version": "1.22.10"}