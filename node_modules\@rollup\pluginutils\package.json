{"_from": "@rollup/pluginutils@^5.0.5", "_id": "@rollup/pluginutils@5.2.0", "_inBundle": false, "_integrity": "sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw==", "_location": "/@rollup/pluginutils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@rollup/pluginutils@^5.0.5", "name": "@rollup/pluginutils", "escapedName": "@rollup%2fpluginutils", "scope": "@rollup", "rawSpec": "^5.0.5", "saveSpec": null, "fetchSpec": "^5.0.5"}, "_requiredBy": ["/unimport", "/unplugin-auto-import", "/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.2.0.tgz", "_shasum": "eac25ca5b0bdda4ba735ddaca5fbf26bd435f602", "_spec": "@rollup/pluginutils@^5.0.5", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "ava": {"extensions": ["ts"], "require": ["ts-node/register"], "workerThreads": false, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "bugs": {"url": "https://github.com/rollup/plugins/issues"}, "bundleDependencies": false, "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "deprecated": false, "description": "A set of utility functions commonly used by Rollup plugins", "devDependencies": {"@rollup/plugin-commonjs": "^23.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^9.0.1", "@types/node": "^14.18.30", "@types/picomatch": "^2.3.0", "acorn": "^8.8.0", "rollup": "^4.0.0-24", "typescript": "^4.8.3"}, "engines": {"node": ">=14.0.0"}, "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "homepage": "https://github.com/rollup/plugins/tree/master/packages/pluginutils#readme", "keywords": ["rollup", "plugin", "utils"], "license": "MIT", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "name": "@rollup/pluginutils", "nyc": {"extension": [".js", ".ts"]}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "publishConfig": {"access": "public"}, "repository": {"url": "git+https://github.com/rollup/plugins.git", "directory": "packages/pluginutils"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build --sourcemap", "release": "pnpm --workspace-root package:release $(pwd)", "test": "ava", "test:ts": "tsc --noEmit"}, "type": "commonjs", "types": "./types/index.d.ts", "version": "5.2.0"}