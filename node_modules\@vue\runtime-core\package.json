{"_from": "@vue/runtime-core@3.5.17", "_id": "@vue/runtime-core@3.5.17", "_inBundle": false, "_integrity": "sha512-QQLXa20dHg1R0ri4bjKeGFKEkJA7MMBxrKo2G+gJikmumRS7PTD4BOU9FKrDQWMKowz7frJJGqBffYMgQYS96Q==", "_location": "/@vue/runtime-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-core@3.5.17", "name": "@vue/runtime-core", "escapedName": "@vue%2fruntime-core", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/@vue/runtime-dom"], "_resolved": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.17.tgz", "_shasum": "b17bd41e13011e85e9b1025545292d43f5512730", "_spec": "@vue/runtime-core@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/@vue/runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeCore", "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/reactivity": "3.5.17", "@vue/shared": "3.5.17"}, "deprecated": false, "description": "@vue/runtime-core", "exports": {".": {"types": "./dist/runtime-core.d.ts", "node": {"production": "./dist/runtime-core.cjs.prod.js", "development": "./dist/runtime-core.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-core.esm-bundler.js", "import": "./dist/runtime-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-core.esm-bundler.js", "name": "@vue/runtime-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-core"}, "sideEffects": false, "types": "dist/runtime-core.d.ts", "version": "3.5.17"}