{"_from": "fast-glob@^3.3.1", "_id": "fast-glob@3.3.3", "_inBundle": false, "_integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "_location": "/fast-glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fast-glob@^3.3.1", "name": "fast-glob", "escapedName": "fast-glob", "rawSpec": "^3.3.1", "saveSpec": null, "fetchSpec": "^3.3.1"}, "_requiredBy": ["/unimport", "/unplugin-auto-import", "/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "_shasum": "d06d585ce8dba90a16b0505c543c3ccfb3aeb818", "_spec": "fast-glob@^3.3.1", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "author": {"name": "<PERSON>", "url": "https://mrmlnc.com"}, "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "bundleDependencies": false, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "deprecated": false, "description": "It's a very fast and efficient glob library for Node.js", "devDependencies": {"@nodelib/fs.macchiato": "^1.0.1", "@types/glob-parent": "^5.1.0", "@types/merge2": "^1.1.4", "@types/micromatch": "^4.0.0", "@types/mocha": "^5.2.7", "@types/node": "^14.18.53", "@types/picomatch": "^2.3.0", "@types/sinon": "^7.5.0", "bencho": "^0.1.1", "eslint": "^6.5.1", "eslint-config-mrmlnc": "^1.1.0", "execa": "^7.1.1", "fast-glob": "^3.0.4", "fdir": "6.0.1", "glob": "^10.0.0", "hereby": "^1.8.1", "mocha": "^6.2.1", "rimraf": "^5.0.0", "sinon": "^7.5.0", "snap-shot-it": "^7.9.10", "typescript": "^4.9.5"}, "engines": {"node": ">=8.6.0"}, "files": ["out", "!out/{benchmark,tests}", "!out/**/*.map", "!out/**/*.spec.*"], "homepage": "https://github.com/mrmlnc/fast-glob#readme", "keywords": ["glob", "patterns", "fast", "implementation"], "license": "MIT", "main": "out/index.js", "name": "fast-glob", "repository": {"type": "git", "url": "git+https://github.com/mrmlnc/fast-glob.git"}, "scripts": {"bench:async": "npm run bench:product:async && npm run bench:regression:async", "bench:product": "npm run bench:product:async && npm run bench:product:sync && npm run bench:product:stream", "bench:product:async": "hereby bench:product:async", "bench:product:stream": "hereby bench:product:stream", "bench:product:sync": "hereby bench:product:sync", "bench:regression": "npm run bench:regression:async && npm run bench:regression:sync && npm run bench:regression:stream", "bench:regression:async": "hereby bench:regression:async", "bench:regression:stream": "hereby bench:regression:stream", "bench:regression:sync": "hereby bench:regression:sync", "bench:stream": "npm run bench:product:stream && npm run bench:regression:stream", "bench:sync": "npm run bench:product:sync && npm run bench:regression:sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "compile": "tsc", "lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "test:e2e": "mocha \"out/**/*.e2e.js\" -s 0", "test:e2e:async": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(async\\)\"", "test:e2e:stream": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(stream\\)\"", "test:e2e:sync": "mocha \"out/**/*.e2e.js\" -s 0 --grep \"\\(sync\\)\"", "watch": "npm run clean && npm run compile -- -- --sourceMap --watch"}, "typings": "out/index.d.ts", "version": "3.3.3"}