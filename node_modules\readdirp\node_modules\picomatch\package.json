{"_from": "picomatch@^2.2.1", "_id": "picomatch@2.3.1", "_inBundle": false, "_integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "_location": "/readdirp/picomatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "picomatch@^2.2.1", "name": "picomatch", "escapedName": "picomatch", "rawSpec": "^2.2.1", "saveSpec": null, "fetchSpec": "^2.2.1"}, "_requiredBy": ["/readdirp"], "_resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "_shasum": "3ba3833733646d9d3e4995946c1365a67fb07a42", "_spec": "picomatch@^2.2.1", "_where": "/mnt/e/www/demo1/node_modules/readdirp", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "engines": {"node": ">=8.6"}, "files": ["index.js", "lib"], "funding": "https://github.com/sponsors/jonschlinkert", "homepage": "https://github.com/micromatch/picomatch", "keywords": ["glob", "match", "picomatch"], "license": "MIT", "main": "index.js", "name": "picomatch", "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/micromatch/picomatch.git"}, "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --reporter dot", "test": "npm run lint && npm run mocha", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "verb": {"toc": {"render": true, "method": "preWrite", "maxdepth": 3}, "layout": "empty", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "version": "2.3.1"}