{"_from": "@vue/shared@3.5.17", "_id": "@vue/shared@3.5.17", "_inBundle": false, "_integrity": "sha512-CabR+UN630VnsJO/jHWYBC1YVXyMq94KKp6iF5MQgZJs5I8cmjw6oVMO1oDbtBkENSHSSn/UadWlW/OAgdmKrg==", "_location": "/@vue/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/shared@3.5.17", "name": "@vue/shared", "escapedName": "@vue%2fshared", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-dom", "/@vue/compiler-sfc", "/@vue/compiler-ssr", "/@vue/reactivity", "/@vue/runtime-core", "/@vue/runtime-dom", "/@vue/server-renderer", "/vue"], "_resolved": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.17.tgz", "_shasum": "e8b3a41f0be76499882a89e8ed40d86a70fa4b70", "_spec": "@vue/shared@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "deprecated": false, "description": "internal utils shared across @vue packages", "exports": {".": {"types": "./dist/shared.d.ts", "node": {"production": "./dist/shared.cjs.prod.js", "development": "./dist/shared.cjs.js", "default": "./index.js"}, "module": "./dist/shared.esm-bundler.js", "import": "./dist/shared.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/shared.esm-bundler.js", "name": "@vue/shared", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "sideEffects": false, "types": "dist/shared.d.ts", "version": "3.5.17"}