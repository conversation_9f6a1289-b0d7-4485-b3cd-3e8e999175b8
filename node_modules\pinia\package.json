{"_from": "pinia@^2.1.0", "_id": "pinia@2.3.1", "_inBundle": false, "_integrity": "sha512-khUlZSwt9xXCaTbbxFYBKDc/bWAGWJjOgvxETwkTN7KRm66EeT1ZdZj6i2ceh9sP2Pzqsbc704r2yngBrxBVug==", "_location": "/pinia", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pinia@^2.1.0", "name": "pinia", "escapedName": "pinia", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/pinia/-/pinia-2.3.1.tgz", "_shasum": "54c476675b72f5abcfafa24a7582531ea8c23d94", "_spec": "pinia@^2.1.0", "_where": "/mnt/e/www/demo1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/vuejs/pinia/issues"}, "bundleDependencies": false, "dependencies": {"@vue/devtools-api": "^6.6.3", "vue-demi": "^0.14.10"}, "deprecated": false, "description": "Intuitive, type safe and flexible Store for Vue", "devDependencies": {"@microsoft/api-extractor": "7.48.0", "@vue/test-utils": "^2.4.6"}, "exports": {".": {"types": "./dist/pinia.d.ts", "node": {"import": {"production": "./dist/pinia.prod.cjs", "development": "./dist/pinia.mjs", "default": "./dist/pinia.mjs"}, "require": {"production": "./dist/pinia.prod.cjs", "development": "./dist/pinia.cjs", "default": "./index.js"}}, "import": "./dist/pinia.mjs", "require": "./index.js"}, "./package.json": "./package.json", "./dist/*": "./dist/*"}, "files": ["dist/*.js", "dist/*.mjs", "dist/*.cjs", "dist/pinia.d.ts", "index.js", "index.cjs", "LICENSE", "README.md"], "funding": "https://github.com/sponsors/posva", "homepage": "https://github.com/vuejs/pinia#readme", "jsdelivr": "dist/pinia.iife.js", "keywords": ["vue", "vuex", "store", "pinia", "piña", "pigna", "composition", "api", "setup", "typed", "typescript", "ts", "type", "safe"], "license": "MIT", "main": "index.js", "module": "dist/pinia.mjs", "name": "pinia", "peerDependencies": {"vue": "^2.7.0 || ^3.5.11", "typescript": ">=4.4.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/pinia.git"}, "scripts": {"build": "rimraf dist && rollup -c ../../rollup.config.mjs --environment TARGET:pinia", "build:dts": "api-extractor run --local --verbose && tail -n +3 ./src/globalExtensions.ts >> dist/pinia.d.ts", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . -l pinia -r 1", "test": "pnpm run build && pnpm run build:dts && pnpm test:dts", "test:dts": "tsc -p ./test-dts/tsconfig.json"}, "sideEffects": false, "types": "dist/pinia.d.ts", "unpkg": "dist/pinia.iife.js", "version": "2.3.1"}