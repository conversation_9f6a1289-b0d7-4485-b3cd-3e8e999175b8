{"_from": "@vue/runtime-dom@3.5.17", "_id": "@vue/runtime-dom@3.5.17", "_inBundle": false, "_integrity": "sha512-8El0M60TcwZ1QMz4/os2MdlQECgGoVHPuLnQBU3m9h3gdNRW9xRmI8iLS4t/22OQlOE6aJvNNlBiCzPHur4H9g==", "_location": "/@vue/runtime-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-dom@3.5.17", "name": "@vue/runtime-dom", "escapedName": "@vue%2fruntime-dom", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.17.tgz", "_shasum": "8e325e29cd03097fe179032fc8df384a426fc83a", "_spec": "@vue/runtime-dom@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeDOM", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/reactivity": "3.5.17", "@vue/runtime-core": "3.5.17", "@vue/shared": "3.5.17", "csstype": "^3.1.3"}, "deprecated": false, "description": "@vue/runtime-dom", "devDependencies": {"@types/trusted-types": "^2.0.7"}, "exports": {".": {"types": "./dist/runtime-dom.d.ts", "node": {"production": "./dist/runtime-dom.cjs.prod.js", "development": "./dist/runtime-dom.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-dom.esm-bundler.js", "import": "./dist/runtime-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-dom#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-dom.esm-bundler.js", "name": "@vue/runtime-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-dom"}, "sideEffects": false, "types": "dist/runtime-dom.d.ts", "unpkg": "dist/runtime-dom.global.js", "version": "3.5.17"}