{"_from": "lodash-unified@^1.0.2", "_id": "lodash-unified@1.0.3", "_inBundle": false, "_integrity": "sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==", "_location": "/lodash-unified", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash-unified@^1.0.2", "name": "lodash-unified", "escapedName": "lodash-unified", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/lodash-unified/-/lodash-unified-1.0.3.tgz", "_shasum": "80b1eac10ed2eb02ed189f08614a29c27d07c894", "_spec": "lodash-unified@^1.0.2", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "Jack <PERSON>"}, "bundleDependencies": false, "deprecated": false, "description": "A union entrypoint of lodash for both ESModule and Commonjs.", "devDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}, "exports": {"import": {"types": "./type.d.ts", "default": "./import.js"}, "require": {"types": "./type.d.ts", "default": "./require.cjs"}}, "license": "MIT", "main": "./require.cjs", "name": "lodash-unified", "peerDependencies": {"lodash": "*", "lodash-es": "*", "@types/lodash-es": "*"}, "type": "module", "types": "./type.d.ts", "version": "1.0.3"}