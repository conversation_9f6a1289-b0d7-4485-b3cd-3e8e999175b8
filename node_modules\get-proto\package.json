{"_from": "get-proto@^1.0.1", "_id": "get-proto@1.0.1", "_inBundle": false, "_integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "_location": "/get-proto", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-proto@^1.0.1", "name": "get-proto", "escapedName": "get-proto", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/get-intrinsic"], "_resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "_shasum": "150b3f2743869ef3e851ec0c49d15b1d14d00ee1", "_spec": "get-proto@^1.0.1", "_where": "/mnt/e/www/demo1/node_modules/get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/get-proto/issues"}, "bundleDependencies": false, "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "deprecated": false, "description": "Robustly get the [[Prototype]] of an object", "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./Reflect.getPrototypeOf": "./Reflect.getPrototypeOf.js", "./Object.getPrototypeOf": "./Object.getPrototypeOf.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/get-proto#readme", "keywords": ["get", "proto", "prototype", "getPrototypeOf", "[[Prototype]]"], "license": "MIT", "main": "index.js", "name": "get-proto", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-proto.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">=10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.1"}