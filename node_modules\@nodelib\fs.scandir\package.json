{"_from": "@nodelib/fs.scandir@2.1.5", "_id": "@nodelib/fs.scandir@2.1.5", "_inBundle": false, "_integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "_location": "/@nodelib/fs.scandir", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@nodelib/fs.scandir@2.1.5", "name": "@nodelib/fs.scandir", "escapedName": "@nodelib%2ffs.scandir", "scope": "@nodelib", "rawSpec": "2.1.5", "saveSpec": null, "fetchSpec": "2.1.5"}, "_requiredBy": ["/@nodelib/fs.walk"], "_resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "_shasum": "7619c2eb21b25483f6d167548b4cfd5a7488c3d5", "_spec": "@nodelib/fs.scandir@2.1.5", "_where": "/mnt/e/www/demo1/node_modules/@nodelib/fs.walk", "bundleDependencies": false, "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "deprecated": false, "description": "List files and directories inside the specified directory", "devDependencies": {"@nodelib/fs.macchiato": "1.0.4", "@types/run-parallel": "^1.1.0"}, "engines": {"node": ">= 8"}, "files": ["out/**", "!out/**/*.map", "!out/**/*.spec.*"], "gitHead": "d6a7960d5281d3dd5f8e2efba49bb552d090f562", "keywords": ["NodeLib", "fs", "FileSystem", "file system", "scandir", "readdir", "dirent"], "license": "MIT", "main": "out/index.js", "name": "@nodelib/fs.scandir", "repository": {"type": "git", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir"}, "scripts": {"build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "rimraf {tsconfig.tsbuildinfo,out}", "compile": "tsc -b .", "compile:watch": "tsc -p . --watch --sourceMap", "lint": "eslint \"src/**/*.ts\" --cache", "test": "mocha \"out/**/*.spec.js\" -s 0", "watch": "npm run clean && npm run compile:watch"}, "typings": "out/index.d.ts", "version": "2.1.5"}