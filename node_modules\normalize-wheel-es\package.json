{"_from": "normalize-wheel-es@^1.2.0", "_id": "normalize-wheel-es@1.2.0", "_inBundle": false, "_integrity": "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==", "_location": "/normalize-wheel-es", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-wheel-es@^1.2.0", "name": "normalize-wheel-es", "escapedName": "normalize-wheel-es", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz", "_shasum": "0fa2593d619f7245a541652619105ab076acf09e", "_spec": "normalize-wheel-es@^1.2.0", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "bundleDependencies": false, "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "deprecated": false, "description": "Mouse wheel normalization across multiple multiple browsers.", "devDependencies": {"@swc/core": "^1.2.218", "bumpp": "^8.2.1", "tsup": "^6.1.3"}, "files": ["dist", "index.d.ts"], "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "keywords": ["mouse wheel", "normalization", "browser", "esm"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/index.js", "module": "dist/index.mjs", "name": "normalize-wheel-es", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "scripts": {"build": "tsup", "release": "bumpp"}, "types": "index.d.ts", "version": "1.2.0"}