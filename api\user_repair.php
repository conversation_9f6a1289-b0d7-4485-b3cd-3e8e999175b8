<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入配置文件
require_once '../config/database.php';

// 用户数据修复类
class UserRepair {
    private $db;
    private $logger;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = Logger::getInstance();
    }
    
    public function processRequest($data) {
        try {
            // 验证必要参数
            if (!isset($data['table_name']) || !isset($data['action']) || !isset($data['query_field'])) {
                throw new Exception("缺少必要参数");
            }
            
            $tableName = $this->sanitizeTableName($data['table_name']);
            $action = $data['action'];
            $queryField = $data['query_field'];
            
            // 根据动作执行不同操作
            switch ($action) {
                case 'update':
                    return $this->updateRecord($tableName, $queryField, $data);
                case 'insert':
                    return $this->insertRecord($tableName, $data);
                case 'delete':
                    return $this->deleteRecord($tableName, $queryField, $data);
                default:
                    throw new Exception("不支持的操作类型: " . $action);
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function updateRecord($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        // 构建更新字段
        $updateFields = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field']) && $key !== $queryField) {
                $updateFields[] = "`{$key}` = :{$key}";
                $params[$key] = $value;
            }
        }
        
        if (empty($updateFields)) {
            throw new Exception("没有要更新的字段");
        }
        
        if (!isset($data[$queryField])) {
            throw new Exception("缺少查询字段值");
        }
        
        $sql = "UPDATE `{$tableName}` SET " . implode(', ', $updateFields) . " WHERE `{$queryField}` = :query_value";
        $params['query_value'] = $data[$queryField];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        if ($affectedRows > 0) {
            return [
                'success' => true,
                'message' => "成功更新 {$affectedRows} 条记录",
                'affected_rows' => $affectedRows
            ];
        } else {
            return [
                'success' => false,
                'message' => "没有找到匹配的记录或数据未发生变化"
            ];
        }
    }
    
    private function insertRecord($tableName, $data) {
        $conn = $this->db->getConnection();
        
        // 构建插入字段
        $fields = [];
        $placeholders = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field'])) {
                $fields[] = "`{$key}`";
                $placeholders[] = ":{$key}";
                $params[$key] = $value;
            }
        }
        
        if (empty($fields)) {
            throw new Exception("没有要插入的字段");
        }
        
        $sql = "INSERT INTO `{$tableName}` (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        return [
            'success' => true,
            'message' => "成功插入记录",
            'insert_id' => $conn->lastInsertId()
        ];
    }
    
    private function deleteRecord($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        if (!isset($data[$queryField])) {
            throw new Exception("缺少查询字段值");
        }
        
        $sql = "DELETE FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $params = ['query_value' => $data[$queryField]];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        if ($affectedRows > 0) {
            return [
                'success' => true,
                'message' => "成功删除 {$affectedRows} 条记录",
                'affected_rows' => $affectedRows
            ];
        } else {
            return [
                'success' => false,
                'message' => "没有找到匹配的记录"
            ];
        }
    }
    
    private function sanitizeTableName($tableName) {
        // 只允许字母、数字和下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        return $tableName;
    }
}

// 主处理逻辑
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("只支持POST请求");
    }
    
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("无效的JSON数据");
    }
    
    $userRepair = new UserRepair();
    $result = $userRepair->processRequest($data);
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
