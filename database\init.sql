-- 数据修复工具数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `repair_tool` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `repair_tool`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `email` varchar(100) NOT NULL,
    `status` enum('active','inactive','banned') DEFAULT 'active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `username` (`username`),
    UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建订单表
CREATE TABLE IF NOT EXISTS `orders` (
    `order_id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `status` enum('pending','paid','shipped','delivered','cancelled') DEFAULT 'pending',
    `order_number` varchar(50) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`order_id`),
    UNIQUE KEY `order_number` (`order_number`),
    KEY `user_id` (`user_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建商品表
CREATE TABLE IF NOT EXISTS `products` (
    `product_id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `sku` varchar(100) DEFAULT NULL,
    `price` decimal(10,2) NOT NULL,
    `stock` int(11) NOT NULL DEFAULT '0',
    `description` text,
    `status` enum('active','inactive','discontinued') DEFAULT 'active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`product_id`),
    UNIQUE KEY `sku` (`sku`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例用户数据
INSERT INTO `users` (`username`, `email`, `status`) VALUES
('admin', '<EMAIL>', 'active'),
('user1', '<EMAIL>', 'active'),
('user2', '<EMAIL>', 'inactive'),
('user3', '<EMAIL>', 'active'),
('user4', '<EMAIL>', 'banned');

-- 插入示例商品数据
INSERT INTO `products` (`name`, `sku`, `price`, `stock`, `description`, `status`) VALUES
('iPhone 15', 'IP15-001', 999.99, 50, 'Latest iPhone model', 'active'),
('Samsung Galaxy S24', 'SG24-001', 899.99, 30, 'Latest Samsung flagship', 'active'),
('MacBook Pro', 'MBP-001', 1999.99, 20, 'Professional laptop', 'active'),
('iPad Air', 'IPA-001', 599.99, 40, 'Tablet device', 'active'),
('AirPods Pro', 'APP-001', 249.99, 100, 'Wireless earphones', 'active');

-- 插入示例订单数据
INSERT INTO `orders` (`user_id`, `amount`, `status`, `order_number`) VALUES
(1, 999.99, 'paid', 'ORD-2024-001'),
(2, 899.99, 'pending', 'ORD-2024-002'),
(3, 1999.99, 'shipped', 'ORD-2024-003'),
(4, 599.99, 'delivered', 'ORD-2024-004'),
(1, 249.99, 'cancelled', 'ORD-2024-005');

-- 创建修复日志表（可选，用于记录修复操作）
CREATE TABLE IF NOT EXISTS `repair_logs` (
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `table_name` varchar(50) NOT NULL,
    `action` enum('update','insert','delete') NOT NULL,
    `query_field` varchar(50) NOT NULL,
    `query_value` varchar(255) NOT NULL,
    `data_before` json DEFAULT NULL,
    `data_after` json DEFAULT NULL,
    `success` tinyint(1) NOT NULL,
    `message` text,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`log_id`),
    KEY `table_name` (`table_name`),
    KEY `action` (`action`),
    KEY `success` (`success`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建配置表（可选，用于存储工具配置）
CREATE TABLE IF NOT EXISTS `repair_configs` (
    `config_id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(50) NOT NULL,
    `config_value` text NOT NULL,
    `description` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`config_id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认配置
INSERT INTO `repair_configs` (`config_key`, `config_value`, `description`) VALUES
('max_threads', '100', '最大并发线程数'),
('timeout', '30', '请求超时时间（秒）'),
('log_enabled', '1', '是否启用操作日志'),
('allowed_tables', 'users,orders,products', '允许操作的表名列表');

-- 创建视图：用户订单统计
CREATE VIEW `user_order_stats` AS
SELECT 
    u.user_id,
    u.username,
    u.email,
    COUNT(o.order_id) as total_orders,
    COALESCE(SUM(o.amount), 0) as total_amount,
    MAX(o.created_at) as last_order_date
FROM users u
LEFT JOIN orders o ON u.user_id = o.user_id
GROUP BY u.user_id, u.username, u.email;

-- 创建索引优化查询性能
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_repair_logs_table_action ON repair_logs(table_name, action);

-- 创建存储过程：批量更新用户状态
DELIMITER //
CREATE PROCEDURE UpdateUserStatus(
    IN p_user_ids TEXT,
    IN p_new_status ENUM('active','inactive','banned')
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id INT;
    DECLARE cur CURSOR FOR 
        SELECT CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(p_user_ids, ',', numbers.n), ',', -1) AS UNSIGNED) as user_id
        FROM (
            SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
            UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
        ) numbers
        WHERE CHAR_LENGTH(p_user_ids) - CHAR_LENGTH(REPLACE(p_user_ids, ',', '')) >= numbers.n - 1;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE users SET status = p_new_status WHERE user_id = user_id;
    END LOOP;
    
    CLOSE cur;
END //
DELIMITER ;

-- 创建触发器：记录用户状态变更
DELIMITER //
CREATE TRIGGER user_status_change_log 
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO repair_logs (
            table_name, 
            action, 
            query_field, 
            query_value, 
            data_before, 
            data_after, 
            success, 
            message
        ) VALUES (
            'users',
            'update',
            'user_id',
            NEW.user_id,
            JSON_OBJECT('status', OLD.status),
            JSON_OBJECT('status', NEW.status),
            1,
            CONCAT('用户状态从 ', OLD.status, ' 变更为 ', NEW.status)
        );
    END IF;
END //
DELIMITER ;

-- 授权（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON repair_tool.* TO 'repair_user'@'localhost' IDENTIFIED BY 'repair_password';
-- FLUSH PRIVILEGES;
