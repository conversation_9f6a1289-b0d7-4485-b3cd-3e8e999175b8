{"_from": "merge2@^1.3.0", "_id": "merge2@1.4.1", "_inBundle": false, "_integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "_location": "/merge2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "merge2@^1.3.0", "name": "merge2", "escapedName": "merge2", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/fast-glob"], "_resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "_shasum": "4368892f885e907455a6fd7dc55c0c9d404990ae", "_spec": "merge2@^1.3.0", "_where": "/mnt/e/www/demo1/node_modules/fast-glob", "authors": ["<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/teambition/merge2/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Merge multiple streams into one stream in sequence or parallel.", "devDependencies": {"standard": "^14.3.4", "through2": "^3.0.1", "thunks": "^4.9.6", "tman": "^1.10.0", "to-through": "^2.0.0"}, "engines": {"node": ">= 8"}, "files": ["README.md", "index.js"], "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "license": "MIT", "main": "./index.js", "name": "merge2", "repository": {"type": "git", "url": "git+ssh://**************/teambition/merge2.git"}, "scripts": {"test": "standard && tman"}, "version": "1.4.1"}