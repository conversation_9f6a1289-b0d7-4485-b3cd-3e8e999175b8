{"_from": "escape-string-regexp@^5.0.0", "_id": "escape-string-regexp@5.0.0", "_inBundle": false, "_integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==", "_location": "/escape-string-regexp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "escape-string-regexp@^5.0.0", "name": "escape-string-regexp", "escapedName": "escape-string-regexp", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/unimport"], "_resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "_shasum": "4683126b500b61762f2dbebace1806e8be31b1c8", "_spec": "escape-string-regexp@^5.0.0", "_where": "/mnt/e/www/demo1/node_modules/unimport", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Escape RegExp special characters", "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "engines": {"node": ">=12"}, "exports": "./index.js", "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "license": "MIT", "name": "escape-string-regexp", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "scripts": {"test": "xo && ava && tsd"}, "type": "module", "version": "5.0.0"}