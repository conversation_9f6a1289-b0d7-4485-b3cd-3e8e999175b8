{"_from": "brace-expansion@^2.0.1", "_id": "brace-expansion@2.0.2", "_inBundle": false, "_integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "_location": "/brace-expansion", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "brace-expansion@^2.0.1", "name": "brace-expansion", "escapedName": "brace-expansion", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/minimatch"], "_resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "_shasum": "54fc53237a613d854c7bd37463aad17df87214e7", "_spec": "brace-expansion@^2.0.1", "_where": "/mnt/e/www/demo1/node_modules/minimatch", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "bundleDependencies": false, "dependencies": {"balanced-match": "^1.0.0"}, "deprecated": false, "description": "Brace expansion as known from sh/bash", "devDependencies": {"@c4312/matcha": "^1.3.1", "tape": "^4.6.0"}, "homepage": "https://github.com/juliangruber/brace-expansion", "keywords": [], "license": "MIT", "main": "index.js", "name": "brace-expansion", "publishConfig": {"tag": "2.x"}, "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "scripts": {"bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh", "test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "2.0.2"}