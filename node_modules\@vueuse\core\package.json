{"_from": "@vueuse/core@^9.1.0", "_id": "@vueuse/core@9.13.0", "_inBundle": false, "_integrity": "sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==", "_location": "/@vueuse/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vueuse/core@^9.1.0", "name": "@vueuse/core", "escapedName": "@vueuse%2fcore", "scope": "@vueuse", "rawSpec": "^9.1.0", "saveSpec": null, "fetchSpec": "^9.1.0"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/@vueuse/core/-/core-9.13.0.tgz", "_shasum": "2f69e66d1905c1e4eebc249a01759cf88ea00cf4", "_spec": "@vueuse/core@^9.1.0", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON>", "email": "https://github.com/antfu"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "bundleDependencies": false, "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "deprecated": false, "description": "Collection of essential Vue Composition Utilities", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*", "./metadata": {"types": "./metadata.d.ts", "require": "./metadata.cjs", "import": "./metadata.mjs"}}, "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse#readme", "jsdelivr": "./index.iife.min.js", "keywords": ["vue", "vue-use", "utils"], "license": "MIT", "main": "./index.cjs", "module": "./index.mjs", "name": "@vueuse/core", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/core"}, "sideEffects": false, "types": "./index.d.ts", "unpkg": "./index.iife.min.js", "version": "9.13.0"}