{"_from": "unplugin-auto-import@^0.16.0", "_id": "unplugin-auto-import@0.16.7", "_inBundle": false, "_integrity": "sha512-w7XmnRlchq6YUFJVFGSvG1T/6j8GrdYN6Em9Wf0Ye+HXgD/22kont+WnuCAA0UaUoxtuvRR1u/mXKy63g/hfqQ==", "_location": "/unplugin-auto-import", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unplugin-auto-import@^0.16.0", "name": "unplugin-auto-import", "escapedName": "unplugin-auto-import", "rawSpec": "^0.16.0", "saveSpec": null, "fetchSpec": "^0.16.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/unplugin-auto-import/-/unplugin-auto-import-0.16.7.tgz", "_shasum": "f4f1f7ab3fba24129bc38e47f83782684030d6e4", "_spec": "unplugin-auto-import@^0.16.0", "_where": "/mnt/e/www/demo1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/unplugin-auto-import/issues"}, "bundleDependencies": false, "dependencies": {"@antfu/utils": "^0.7.6", "@rollup/pluginutils": "^5.0.5", "fast-glob": "^3.3.1", "local-pkg": "^0.5.0", "magic-string": "^0.30.5", "minimatch": "^9.0.3", "unimport": "^3.4.0", "unplugin": "^1.5.0"}, "deprecated": false, "description": "Register global imports on demand for Vite and Webpack", "devDependencies": {"@antfu/eslint-config": "^1.0.0-beta.29", "@antfu/ni": "^0.21.8", "@nuxt/kit": "^3.8.0", "@types/node": "^20.8.9", "@types/resolve": "^1.20.4", "@vueuse/metadata": "^10.5.0", "bumpp": "^9.2.0", "eslint": "^8.52.0", "esno": "^0.17.0", "rollup": "^4.1.4", "tsup": "^7.2.0", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "webpack": "^5.89.0"}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./nuxt": {"types": "./dist/nuxt.d.ts", "import": "./dist/nuxt.js", "require": "./dist/nuxt.cjs"}, "./astro": {"types": "./dist/astro.d.ts", "import": "./dist/astro.js", "require": "./dist/astro.cjs"}, "./rollup": {"types": "./dist/rollup.d.ts", "import": "./dist/rollup.js", "require": "./dist/rollup.cjs"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js", "require": "./dist/types.cjs"}, "./vite": {"types": "./dist/vite.d.ts", "import": "./dist/vite.js", "require": "./dist/vite.cjs"}, "./webpack": {"types": "./dist/webpack.d.ts", "import": "./dist/webpack.js", "require": "./dist/webpack.cjs"}, "./rspack": {"types": "./dist/rspack.d.ts", "import": "./dist/rspack.js", "require": "./dist/rspack.cjs"}, "./esbuild": {"types": "./dist/esbuild.d.ts", "import": "./dist/esbuild.js", "require": "./dist/esbuild.cjs"}, "./*": "./*"}, "files": ["*.d.ts", "dist"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unplugin-auto-import#readme", "keywords": ["unplugin", "vite", "astro", "webpack", "rollup", "rspack", "auto-import", "transform"], "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.js", "name": "unplugin-auto-import", "packageManager": "pnpm@8.9.2", "peerDependencies": {"@nuxt/kit": "^3.2.2", "@vueuse/core": "*"}, "peerDependenciesMeta": {"@vueuse/core": {"optional": true}, "@nuxt/kit": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/antfu/unplugin-auto-import.git"}, "scripts": {"build": "tsup src/*.ts --format cjs,esm --dts --splitting --clean && esno scripts/postbuild.ts", "dev": "tsup src/*.ts --watch src", "lint": "eslint .", "lint:fix": "nr lint --fix", "play": "npm -C playground run dev", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "test:run": "vitest run"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "version": "0.16.7"}