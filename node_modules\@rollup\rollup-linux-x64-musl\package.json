{"_from": "@rollup/rollup-linux-x64-musl@4.45.1", "_id": "@rollup/rollup-linux-x64-musl@4.45.1", "_inBundle": false, "_integrity": "sha512-a6WIAp89p3kpNoYStITT9RbTbTnqarU7D8N8F2CV+4Cl9fwCOZraLVuVFvlpsW0SbIiYtEnhCZBPLoNdRkjQFw==", "_location": "/@rollup/rollup-linux-x64-musl", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@rollup/rollup-linux-x64-musl@4.45.1", "name": "@rollup/rollup-linux-x64-musl", "escapedName": "@rollup%2frollup-linux-x64-musl", "scope": "@rollup", "rawSpec": "4.45.1", "saveSpec": null, "fetchSpec": "4.45.1"}, "_requiredBy": ["/rollup"], "_resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.45.1.tgz", "_shasum": "ec30bb48b5fe22a3aaba98072f2d5b7139e1a8eb", "_spec": "@rollup/rollup-linux-x64-musl@4.45.1", "_where": "/mnt/e/www/demo1/node_modules/rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "Native bindings for Rollup", "files": ["rollup.linux-x64-musl.node"], "homepage": "https://rollupjs.org/", "libc": ["musl"], "license": "MIT", "main": "./rollup.linux-x64-musl.node", "name": "@rollup/rollup-linux-x64-musl", "os": ["linux"], "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "version": "4.45.1"}