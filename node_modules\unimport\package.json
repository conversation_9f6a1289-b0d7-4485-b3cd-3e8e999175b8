{"_from": "unimport@^3.4.0", "_id": "unimport@3.14.6", "_inBundle": false, "_integrity": "sha512-CYvbDaTT04Rh8bmD8jz3WPmHYZRG/NnvYVzwD6V1YAlvvKROlAeNDUBhkBGzNav2RKaeuXvlWYaa1V4Lfi/O0g==", "_location": "/unimport", "_phantomChildren": {"@types/estree": "1.0.8", "exsolve": "1.0.7", "mlly": "1.7.4", "pathe": "2.0.3", "quansync": "0.2.10"}, "_requested": {"type": "range", "registry": true, "raw": "unimport@^3.4.0", "name": "unimport", "escapedName": "unimport", "rawSpec": "^3.4.0", "saveSpec": null, "fetchSpec": "^3.4.0"}, "_requiredBy": ["/unplugin-auto-import"], "_resolved": "https://registry.npmjs.org/unimport/-/unimport-3.14.6.tgz", "_shasum": "f01170aa2fb94c4f97b22c0ac2822ef7e8e0726d", "_spec": "unimport@^3.4.0", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "bugs": {"url": "https://github.com/unjs/unimport/issues"}, "bundleDependencies": false, "dependencies": {"@rollup/pluginutils": "^5.1.4", "acorn": "^8.14.0", "escape-string-regexp": "^5.0.0", "estree-walker": "^3.0.3", "fast-glob": "^3.3.3", "local-pkg": "^1.0.0", "magic-string": "^0.30.17", "mlly": "^1.7.4", "pathe": "^2.0.1", "picomatch": "^4.0.2", "pkg-types": "^1.3.0", "scule": "^1.3.0", "strip-literal": "^2.1.1", "unplugin": "^1.16.1"}, "deprecated": false, "description": "Unified utils for auto importing APIs in modules", "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@types/estree": "^1.0.6", "@types/node": "^22.10.6", "@types/picomatch": "^3.0.1", "@vitest/coverage-v8": "^2.1.8", "bumpp": "^9.10.0", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.18.0", "h3": "^1.13.1", "jquery": "^3.7.1", "lit": "^3.2.1", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^2.1.8", "vue-tsc": "^2.2.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./unplugin": {"import": "./dist/unplugin.mjs", "require": "./dist/unplugin.cjs"}, "./addons": {"import": "./dist/addons.mjs", "require": "./dist/addons.cjs"}, "./*": "./*"}, "files": ["*.d.ts", "dist"], "homepage": "https://github.com/unjs/unimport#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "unimport", "repository": {"type": "git", "url": "git+https://github.com/unjs/unimport.git"}, "scripts": {"build": "unbuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "dev": "vitest dev", "lint": "eslint .", "play": "pnpm -C playground run dev", "play:build": "pnpm -C playground run build", "release": "pnpm run test --run && bumpp -x \"pnpm run changelog\" --all && pnpm publish", "test": "vitest --coverage", "typecheck": "vue-tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "./dist/index.d.ts", "version": "3.14.6"}