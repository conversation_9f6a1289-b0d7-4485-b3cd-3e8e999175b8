<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式优化测试 - 数据修复工具</title>
    <!-- Ant Design CSS -->
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-palette text-primary"></i>
                    样式优化测试页面
                </h1>
                <div class="text-center mb-4">
                    <a href="index.html" class="btn btn-primary me-2">
                        <i class="fas fa-home"></i> 返回主页
                    </a>
                    <a href="test-enhanced.html" class="btn btn-info">
                        <i class="fas fa-rocket"></i> 功能测试
                    </a>
                </div>
            </div>
        </div>

        <!-- 样式改进展示 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-brush"></i> 样式改进展示</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-1 text-primary"></i> 优化的表格样式</h6>
                                <div class="table-container">
                                    <div class="table-responsive">
                                        <table class="table editable-table">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        <input type="text" value="用户ID" class="form-control form-control-sm">
                                                        <button type="button" class="delete-col-btn" title="删除列">×</button>
                                                    </th>
                                                    <th>
                                                        <input type="text" value="用户名" class="form-control form-control-sm">
                                                        <button type="button" class="delete-col-btn" title="删除列">×</button>
                                                    </th>
                                                    <th>
                                                        <input type="text" value="邮箱" class="form-control form-control-sm">
                                                        <button type="button" class="delete-col-btn" title="删除列">×</button>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="1">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="张三">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="<EMAIL>">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="2">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="李四">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control form-control-sm" value="<EMAIL>">
                                                        <button type="button" class="delete-row-btn" title="删除行">×</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Ant Design风格的表格，支持悬停效果和焦点状态
                                </small>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-2 text-success"></i> 优化的CSV预览</h6>
                                <div class="csv-preview-container">
                                    <div class="csv-preview-header">
                                        <h6 class="csv-preview-title">
                                            <i class="fas fa-eye me-2"></i>CSV文件预览
                                        </h6>
                                        <span class="csv-preview-info">
                                            总计: 100 行 × 4 列 | 预览: 5 行
                                        </span>
                                    </div>
                                    <div class="csv-preview-table-wrapper">
                                        <table class="csv-preview-table">
                                            <thead>
                                                <tr>
                                                    <th>用户ID</th>
                                                    <th>用户名</th>
                                                    <th>邮箱</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>1</td>
                                                    <td>张三</td>
                                                    <td><EMAIL></td>
                                                    <td>active</td>
                                                </tr>
                                                <tr>
                                                    <td>2</td>
                                                    <td>李四</td>
                                                    <td><EMAIL></td>
                                                    <td>inactive</td>
                                                </tr>
                                                <tr>
                                                    <td>3</td>
                                                    <td>王五</td>
                                                    <td><EMAIL></td>
                                                    <td>active</td>
                                                </tr>
                                                <tr>
                                                    <td>4</td>
                                                    <td>赵六</td>
                                                    <td><EMAIL></td>
                                                    <td>banned</td>
                                                </tr>
                                                <tr>
                                                    <td>5</td>
                                                    <td>钱七</td>
                                                    <td><EMAIL></td>
                                                    <td>active</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    清晰的预览界面，包含统计信息和滚动支持
                                </small>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6><i class="fas fa-3 text-warning"></i> 当前标签页指示器</h6>
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <button class="btn btn-primary me-2">
                                            <i class="fas fa-play"></i> 开始修复
                                        </button>
                                        <button class="btn btn-outline-secondary">
                                            <i class="fas fa-download"></i> 下载模板
                                        </button>
                                    </div>
                                    <div class="current-tab-indicator">
                                        <i class="fas fa-tag"></i>
                                        当前数据类型：<span class="ms-1">用户</span>
                                    </div>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle"></i>
                                    指示器移到按钮右侧，样式更加清晰
                                </small>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-4 text-info"></i> 按钮组优化</h6>
                                <div class="d-flex align-items-center flex-wrap gap-2">
                                    <label class="form-label me-2 mb-0">线程数:</label>
                                    <input type="number" class="form-control me-2" value="50" style="width: 100px;">
                                    <button class="btn btn-outline-info me-2">
                                        <i class="fas fa-download"></i> 下载模板
                                    </button>
                                    <button class="btn btn-primary me-2">
                                        <i class="fas fa-play"></i> 开始修复
                                    </button>
                                    <button class="btn btn-success">
                                        <i class="fas fa-download"></i> 导出结果
                                    </button>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle"></i>
                                    响应式按钮布局，支持换行和间距优化
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 样式对比 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-balance-scale"></i> 样式对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>改进前的问题</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        表格样式单调，缺乏现代感
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        CSV预览界面简陋，信息不足
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        标签页指示器位置不合理
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        按钮布局混乱，缺乏层次
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        整体风格不统一
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>改进后的优势</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Ant Design风格，现代化界面
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        详细的CSV预览，包含统计信息
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        指示器位置合理，视觉平衡
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        响应式按钮布局，层次清晰
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        统一的设计语言和交互体验
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 新功能说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>模板下载功能</h6>
                                <p class="text-muted">
                                    每个标签页都可以下载对应的数据模板，包含示例数据，方便用户快速开始。
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6>标签页模板切换</h6>
                                <p class="text-muted">
                                    切换标签页时，快速修复表格会自动更新为对应的字段模板。
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6>统一设计语言</h6>
                                <p class="text-muted">
                                    采用Ant Design设计规范，提供一致的用户体验和视觉效果。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toast 消息将动态添加到这里 -->
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
