# 数据修复工具部署指南

## 环境要求

### 服务器环境
- **操作系统**: Linux/Windows/macOS
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP**: 7.4+ (推荐 8.0+)
- **MySQL**: 5.7+ 或 MariaDB 10.3+
- **内存**: 最少 512MB，推荐 2GB+
- **磁盘空间**: 最少 100MB

### PHP扩展要求
- PDO
- PDO_MySQL
- JSON
- mbstring
- openssl

## 快速部署

### 1. 下载项目文件
```bash
# 克隆项目或下载压缩包
git clone <repository-url> /var/www/html/repair-tool
cd /var/www/html/repair-tool
```

### 2. 配置数据库

#### 创建数据库
```sql
CREATE DATABASE repair_tool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 导入数据库结构
```bash
mysql -u root -p repair_tool < database/init.sql
```

#### 创建数据库用户（可选）
```sql
CREATE USER 'repair_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON repair_tool.* TO 'repair_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 配置应用

#### 修改数据库配置
编辑 `config/database.php` 文件：
```php
class DatabaseConfig {
    const HOST = 'localhost';
    const USERNAME = 'repair_user';  // 修改为您的数据库用户名
    const PASSWORD = 'your_secure_password';  // 修改为您的数据库密码
    const DATABASE = 'repair_tool';
    const PORT = 3306;
}
```

### 4. 设置文件权限

#### Linux/macOS
```bash
# 设置目录权限
chmod 755 /var/www/html/repair-tool
chmod 755 /var/www/html/repair-tool/api
chmod 755 /var/www/html/repair-tool/config
chmod 755 /var/www/html/repair-tool/css
chmod 755 /var/www/html/repair-tool/js

# 设置文件权限
chmod 644 /var/www/html/repair-tool/*.html
chmod 644 /var/www/html/repair-tool/css/*
chmod 644 /var/www/html/repair-tool/js/*
chmod 644 /var/www/html/repair-tool/api/*.php
chmod 644 /var/www/html/repair-tool/config/*.php

# 创建日志目录
mkdir -p /var/www/html/repair-tool/logs
chmod 755 /var/www/html/repair-tool/logs
chown www-data:www-data /var/www/html/repair-tool/logs
```

#### Windows
```cmd
# 确保IIS_IUSRS有读取权限
icacls "C:\inetpub\wwwroot\repair-tool" /grant IIS_IUSRS:(OI)(CI)R
```

### 5. Web服务器配置

#### Apache配置
创建 `.htaccess` 文件：
```apache
RewriteEngine On

# 安全设置
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<Files "config/*">
    Order deny,allow
    Deny from all
</Files>

<Files "database/*">
    Order deny,allow
    Deny from all
</Files>

<Files "logs/*">
    Order deny,allow
    Deny from all
</Files>

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/repair-tool;
    index index.html index.php;

    # 安全设置
    location ~ ^/(config|database|logs)/ {
        deny all;
        return 403;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## 测试部署

### 1. 访问应用
在浏览器中打开：`http://your-domain.com/repair-tool/`

### 2. 运行测试页面
访问：`http://your-domain.com/repair-tool/test.html`

### 3. 测试API接口
```bash
curl -X POST http://your-domain.com/repair-tool/api/user_repair.php \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "users",
    "action": "update",
    "query_field": "user_id",
    "user_id": "1",
    "username": "test_user"
  }'
```

## 生产环境优化

### 1. 安全加固

#### 隐藏PHP版本信息
在 `php.ini` 中设置：
```ini
expose_php = Off
```

#### 限制文件上传
```ini
file_uploads = Off
upload_max_filesize = 0
```

#### 禁用危险函数
```ini
disable_functions = exec,passthru,shell_exec,system,proc_open,popen
```

### 2. 性能优化

#### 启用OPcache
```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

#### 数据库连接池
考虑使用连接池来优化数据库连接：
```php
// 在config/database.php中添加
const MAX_CONNECTIONS = 20;
const CONNECTION_TIMEOUT = 30;
```

### 3. 监控和日志

#### 设置日志轮转
创建 `/etc/logrotate.d/repair-tool`：
```
/var/www/html/repair-tool/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### 监控脚本
```bash
#!/bin/bash
# monitor.sh - 简单的监控脚本

LOG_FILE="/var/www/html/repair-tool/logs/repair.log"
ERROR_COUNT=$(grep -c "ERROR" "$LOG_FILE" 2>/dev/null || echo 0)

if [ "$ERROR_COUNT" -gt 10 ]; then
    echo "警告：发现 $ERROR_COUNT 个错误" | mail -s "修复工具错误警报" <EMAIL>
fi
```

## 故障排除

### 常见问题

1. **500内部服务器错误**
   - 检查PHP错误日志：`tail -f /var/log/php_errors.log`
   - 验证文件权限
   - 检查数据库连接

2. **数据库连接失败**
   - 验证数据库服务状态：`systemctl status mysql`
   - 检查配置文件中的连接信息
   - 测试数据库连接：`mysql -u username -p`

3. **CORS错误**
   - 检查浏览器控制台
   - 验证API响应头设置
   - 确认域名配置

4. **性能问题**
   - 监控服务器资源使用
   - 检查数据库查询性能
   - 调整并发线程数

### 日志分析
```bash
# 查看错误日志
tail -f logs/repair.log | grep ERROR

# 统计API调用次数
grep "INFO" logs/repair.log | grep -c "processRequest"

# 查看最近的操作
tail -n 100 logs/repair.log
```

## 备份和恢复

### 数据库备份
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p repair_tool > /backup/repair_tool_$DATE.sql
find /backup -name "repair_tool_*.sql" -mtime +7 -delete
```

### 应用备份
```bash
# 备份应用文件
tar -czf /backup/repair-tool_$(date +%Y%m%d).tar.gz /var/www/html/repair-tool
```

## 升级指南

### 1. 备份现有数据
```bash
mysqldump -u root -p repair_tool > backup_before_upgrade.sql
cp -r /var/www/html/repair-tool /backup/repair-tool-old
```

### 2. 下载新版本
```bash
cd /tmp
git clone <new-version-url> repair-tool-new
```

### 3. 更新文件
```bash
cp -r /tmp/repair-tool-new/* /var/www/html/repair-tool/
# 保留配置文件
cp /backup/repair-tool-old/config/database.php /var/www/html/repair-tool/config/
```

### 4. 运行数据库迁移
```bash
mysql -u root -p repair_tool < database/upgrade.sql
```

### 5. 测试功能
访问测试页面确认所有功能正常工作。

## 联系支持

如果在部署过程中遇到问题，请：
1. 检查日志文件
2. 参考故障排除部分
3. 提交Issue并附上错误信息和环境详情
