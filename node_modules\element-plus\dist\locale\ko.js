/*! Element Plus v2.10.4 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleKo = factory());
})(this, (function () { 'use strict';

  var ko = {
    name: "ko",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "\uD655\uC778",
        clear: "\uCD08\uAE30\uD654",
        defaultLabel: "\uC0C9\uC0C1 \uC120\uD0DD\uAE30",
        description: "\uD604\uC7AC \uC0C9\uC0C1\uC740 {color}\uC785\uB2C8\uB2E4. Enter \uD0A4\uB97C \uB20C\uB7EC \uC0C8 \uC0C9\uC0C1\uC744 \uC120\uD0DD\uD569\uB2C8\uB2E4."
      },
      datepicker: {
        now: "\uC9C0\uAE08",
        today: "\uC624\uB298",
        cancel: "\uCDE8\uC18C",
        clear: "\uCD08\uAE30\uD654",
        confirm: "\uD655\uC778",
        dateTablePrompt: "\uD654\uC0B4\uD45C \uD0A4\uB97C \uC0AC\uC6A9\uD558\uACE0 Enter\uB97C \uB20C\uB7EC \uB0A0\uC9DC\uB97C \uC120\uD0DD\uD558\uC2ED\uC2DC\uC624.",
        monthTablePrompt: "\uD654\uC0B4\uD45C \uD0A4\uB97C \uC0AC\uC6A9\uD558\uACE0 Enter\uB97C \uB20C\uB7EC \uC6D4\uC744 \uC120\uD0DD\uD569\uB2C8\uB2E4.",
        yearTablePrompt: "\uD654\uC0B4\uD45C \uD0A4\uB97C \uC0AC\uC6A9\uD558\uACE0 Enter \uD0A4\uB97C \uB20C\uB7EC \uC5F0\uB3C4\uB97C \uC120\uD0DD\uD569\uB2C8\uB2E4.",
        selectDate: "\uB0A0\uC9DC \uC120\uD0DD",
        selectTime: "\uC2DC\uAC04 \uC120\uD0DD",
        startDate: "\uC2DC\uC791 \uB0A0\uC9DC",
        startTime: "\uC2DC\uC791 \uC2DC\uAC04",
        endDate: "\uC885\uB8CC \uB0A0\uC9DC",
        endTime: "\uC885\uB8CC \uC2DC\uAC04",
        prevYear: "\uC9C0\uB09C\uD574",
        nextYear: "\uB2E4\uC74C\uD574",
        prevMonth: "\uC9C0\uB09C\uB2EC",
        nextMonth: "\uB2E4\uC74C\uB2EC",
        year: "\uB144",
        month1: "1\uC6D4",
        month2: "2\uC6D4",
        month3: "3\uC6D4",
        month4: "4\uC6D4",
        month5: "5\uC6D4",
        month6: "6\uC6D4",
        month7: "7\uC6D4",
        month8: "8\uC6D4",
        month9: "9\uC6D4",
        month10: "10\uC6D4",
        month11: "11\uC6D4",
        month12: "12\uC6D4",
        weeks: {
          sun: "\uC77C",
          mon: "\uC6D4",
          tue: "\uD654",
          wed: "\uC218",
          thu: "\uBAA9",
          fri: "\uAE08",
          sat: "\uD1A0"
        },
        months: {
          jan: "1\uC6D4",
          feb: "2\uC6D4",
          mar: "3\uC6D4",
          apr: "4\uC6D4",
          may: "5\uC6D4",
          jun: "6\uC6D4",
          jul: "7\uC6D4",
          aug: "8\uC6D4",
          sep: "9\uC6D4",
          oct: "10\uC6D4",
          nov: "11\uC6D4",
          dec: "12\uC6D4"
        }
      },
      inputNumber: {
        decrease: "\uAC12 \uC99D\uAC00",
        increase: "\uAC12 \uAC10\uC18C"
      },
      select: {
        loading: "\uBD88\uB7EC\uC624\uB294 \uC911",
        noMatch: "\uAC80\uC0C9\uB41C \uB370\uC774\uD130 \uC5C6\uC74C",
        noData: "\uB370\uC774\uD130 \uC5C6\uC74C",
        placeholder: "\uC120\uD0DD"
      },
      mention: {
        loading: "\uBD88\uB7EC\uC624\uB294 \uC911"
      },
      dropdown: {
        toggleDropdown: "\uB4DC\uB86D\uB2E4\uC6B4 \uC804\uD658"
      },
      cascader: {
        noMatch: "\uAC80\uC0C9\uB41C \uB370\uC774\uD130 \uC5C6\uC74C",
        loading: "\uBD88\uB7EC\uC624\uB294 \uC911",
        placeholder: "\uC120\uD0DD",
        noData: "\uB370\uC774\uD130 \uC5C6\uC74C"
      },
      pagination: {
        goto: "\uC774\uB3D9",
        pagesize: "\uAC74/\uD398\uC774\uC9C0",
        total: "\uCD1D {total} \uAC74",
        pageClassifier: "\uD398\uC774\uC9C0\uB85C",
        page: "\uD398\uC774\uC9C0",
        prev: "\uC774\uC804 \uD398\uC774\uC9C0\uB85C \uC774\uB3D9",
        next: "\uB2E4\uC74C \uD398\uC774\uC9C0\uB85C \uC774\uB3D9",
        currentPage: "\uD398\uC774\uC9C0 {pager}",
        prevPages: "\uC774\uC804 {pager} \uD398\uC774\uC9C0",
        nextPages: "\uB2E4\uC74C {pager} \uD398\uC774\uC9C0",
        deprecationWarning: "\uB354 \uC774\uC0C1 \uC0AC\uC6A9\uB418\uC9C0 \uC54A\uB294 \uB3D9\uC791\uC774 \uAC10\uC9C0\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uC790\uC138\uD55C \uB0B4\uC6A9\uC740 el-pagination \uBB38\uC11C\uB97C \uCC38\uC870\uD558\uC138\uC694."
      },
      dialog: {
        close: "\uB300\uD654 \uC0C1\uC790 \uB2EB\uAE30"
      },
      drawer: {
        close: "\uB300\uD654 \uC0C1\uC790 \uB2EB\uAE30"
      },
      messagebox: {
        title: "\uBA54\uC2DC\uC9C0",
        confirm: "\uD655\uC778",
        cancel: "\uCDE8\uC18C",
        error: "\uC62C\uBC14\uB974\uC9C0 \uC54A\uC740 \uC785\uB825",
        close: "\uB300\uD654 \uC0C1\uC790 \uB2EB\uAE30"
      },
      upload: {
        deleteTip: "Delete \uD0A4\uB97C \uB20C\uB7EC \uC0AD\uC81C",
        delete: "\uC0AD\uC81C",
        preview: "\uBBF8\uB9AC\uBCF4\uAE30",
        continue: "\uACC4\uC18D\uD558\uAE30"
      },
      slider: {
        defaultLabel: "{min}\uACFC {max} \uC0AC\uC774\uC758 \uC2AC\uB77C\uC774\uB354",
        defaultRangeStartLabel: "\uC2DC\uC791 \uAC12 \uC120\uD0DD",
        defaultRangeEndLabel: "\uC885\uB8CC \uAC12 \uC120\uD0DD"
      },
      table: {
        emptyText: "\uB370\uC774\uD130 \uC5C6\uC74C",
        confirmFilter: "\uD655\uC778",
        resetFilter: "\uCD08\uAE30\uD654",
        clearFilter: "\uC804\uCCB4",
        sumText: "\uD569\uACC4"
      },
      tour: {
        next: "\uB2E4\uC74C",
        previous: "\uC774\uC804",
        finish: "\uC885\uB8CC"
      },
      tree: {
        emptyText: "\uB370\uC774\uD130 \uC5C6\uC74C"
      },
      transfer: {
        noMatch: "\uAC80\uC0C9\uB41C \uB370\uC774\uD130 \uC5C6\uC74C",
        noData: "\uB370\uC774\uD130 \uC5C6\uC74C",
        titles: ["\uB9AC\uC2A4\uD2B8 1", "\uB9AC\uC2A4\uD2B8 2"],
        filterPlaceholder: "\uAC80\uC0C9\uC5B4\uB97C \uC785\uB825\uD558\uC138\uC694",
        noCheckedFormat: "\uCD1D {total} \uAC74",
        hasCheckedFormat: "{checked}/{total} \uC120\uD0DD\uB428"
      },
      image: {
        error: "\uBD88\uB7EC\uC624\uAE30 \uC2E4\uD328"
      },
      pageHeader: {
        title: "\uB4A4\uB85C"
      },
      popconfirm: {
        confirmButtonText: "\uC608",
        cancelButtonText: "\uC544\uB2C8\uC624"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return ko;

}));
