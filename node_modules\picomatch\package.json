{"_from": "picomatch@^4.0.2", "_id": "picomatch@4.0.3", "_inBundle": false, "_integrity": "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==", "_location": "/picomatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "picomatch@^4.0.2", "name": "picomatch", "escapedName": "picomatch", "rawSpec": "^4.0.2", "saveSpec": null, "fetchSpec": "^4.0.2"}, "_requiredBy": ["/@rollup/pluginutils", "/unimport"], "_resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz", "_shasum": "796c76136d1eead715db1e7bad785dedd695a042", "_spec": "picomatch@^4.0.2", "_where": "/mnt/e/www/demo1/node_modules/@rollup/pluginutils", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "devDependencies": {"eslint": "^8.57.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^10.4.0", "nyc": "^15.1.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "engines": {"node": ">=12"}, "files": ["index.js", "posix.js", "lib"], "funding": "https://github.com/sponsors/jonschlinkert", "homepage": "https://github.com/micromatch/picomatch", "keywords": ["glob", "match", "picomatch"], "license": "MIT", "main": "index.js", "name": "picomatch", "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/micromatch/picomatch.git"}, "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --reporter dot", "test": "npm run lint && npm run mocha", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "sideEffects": false, "verb": {"toc": {"render": true, "method": "preWrite", "maxdepth": 3}, "layout": "empty", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}, "version": "4.0.3"}