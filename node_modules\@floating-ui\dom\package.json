{"_from": "@floating-ui/dom@^1.0.1", "_id": "@floating-ui/dom@1.7.2", "_inBundle": false, "_integrity": "sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==", "_location": "/@floating-ui/dom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@floating-ui/dom@^1.0.1", "name": "@floating-ui/dom", "escapedName": "@floating-ui%2fdom", "scope": "@floating-ui", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz", "_shasum": "3540b051cf5ce0d4f4db5fb2507a76e8ea5b4a45", "_spec": "@floating-ui/dom@^1.0.1", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "atomiks"}, "bugs": {"url": "https://github.com/floating-ui/floating-ui"}, "bundleDependencies": false, "dependencies": {"@floating-ui/core": "^1.7.2", "@floating-ui/utils": "^0.2.10"}, "deprecated": false, "description": "Floating UI for the web", "devDependencies": {"@types/react": "^18.3.19", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "config": "0.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.dom.d.mts", "default": "./dist/floating-ui.dom.mjs"}, "types": "./dist/floating-ui.dom.d.ts", "module": "./dist/floating-ui.dom.esm.js", "default": "./dist/floating-ui.dom.umd.js"}}, "files": ["dist"], "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "license": "MIT", "main": "./dist/floating-ui.dom.umd.js", "module": "./dist/floating-ui.dom.esm.js", "name": "@floating-ui/dom", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/floating-ui/floating-ui.git", "directory": "packages/dom"}, "scripts": {"build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json", "clean": "<PERSON><PERSON><PERSON> dist out-tsc test-results", "dev": "vite", "format": "prettier --write .", "lint": "eslint .", "playwright": "playwright test ./test/functional", "publint": "publint", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc -b"}, "sideEffects": false, "types": "./dist/floating-ui.dom.d.ts", "unpkg": "./dist/floating-ui.dom.umd.min.js", "version": "1.7.2"}