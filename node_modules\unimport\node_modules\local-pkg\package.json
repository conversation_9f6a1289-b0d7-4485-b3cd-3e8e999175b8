{"_from": "local-pkg@^1.0.0", "_id": "local-pkg@1.1.1", "_inBundle": false, "_integrity": "sha512-WunYko2W1NcdfAFpuLUoucsgULmgDBRkdxHxWQ7mK0cQqwPiy8E1enjuRBrhLtZkB5iScJ1XIPdhVEFK8aOLSg==", "_location": "/unimport/local-pkg", "_phantomChildren": {"confbox": "0.2.2", "exsolve": "1.0.7", "pathe": "2.0.3"}, "_requested": {"type": "range", "registry": true, "raw": "local-pkg@^1.0.0", "name": "local-pkg", "escapedName": "local-pkg", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/unimport"], "_resolved": "https://registry.npmjs.org/local-pkg/-/local-pkg-1.1.1.tgz", "_shasum": "f5fe74a97a3bd3c165788ee08ca9fbe998dc58dd", "_spec": "local-pkg@^1.0.0", "_where": "/mnt/e/www/demo1/node_modules/unimport", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "bundleDependencies": false, "dependencies": {"mlly": "^1.7.4", "pkg-types": "^2.0.1", "quansync": "^0.2.8"}, "deprecated": false, "description": "Get information on local packages.", "devDependencies": {"@antfu/eslint-config": "^4.4.0", "@antfu/ni": "^23.3.1", "@antfu/utils": "^9.1.0", "@types/chai": "^5.0.1", "@types/node": "^22.13.8", "bumpp": "^10.0.3", "chai": "^5.2.0", "eslint": "^9.21.0", "esno": "^4.8.0", "find-up-simple": "^1.0.1", "typescript": "^5.8.2", "unbuild": "^3.5.0", "unplugin-quansync": "^0.3.3", "vitest": "^3.0.7"}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "keywords": ["package"], "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.mjs", "name": "local-pkg", "packageManager": "pnpm@10.5.2", "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "scripts": {"build": "unbuild", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp && npm publish", "test": "vitest run && node ./test/cjs.cjs && node ./test/esm.mjs", "typecheck": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "version": "1.1.1"}