// 全局变量
let csvData = [];

// 标签页配置
const tabConfigs = {
    node: {
        tableName: '',
        action: '',
        columns: ['channel', 'sellerId', 'skuId', 'mskuId'],
        apiEndpoints: {
            'listing': {
                'nodeGateway': 'http://172.16.10.62:30015/api/',
                'tableLists': [
                    'amazon-active-listings',
                    'amazon_active_listing_logs',
                    'sku-seller-config',
                    'pid-scu-maps',
                ]
            }
        },
        tableOptions: [
            { value: 'amazon-active-listings', text: 'amazon-active-listings' },
            { value: 'amazon_active_listing_logs', text: 'amazon_active_listing_logs' },
            { value: 'sku-seller-configs', text: 'sku-seller-configs' },
            { value: 'pid-scu-maps', text: 'pid-scu-maps' },
            // 可以在这里添加更多表名选项
        ],
        supportOptions: [
            { value: 'create', text: '新增' },
            { value: 'update', text: '更新' },
            { value: 'delete', text: '删除' },
        ]
    },
    user: {
        tableName: 'users',
        action: 'update',
        queryField: 'user_id',
        columns: ['user_id', 'username', 'email', 'status'],
        apiEndpoint: 'api/user_repair.php'
    },
    order: {
        tableName: 'orders',
        action: 'update',
        queryField: 'order_id',
        columns: ['order_id', 'user_id', 'amount', 'status'],
        apiEndpoint: 'api/order_repair.php'
    },
    product: {
        tableName: 'products',
        action: 'update',
        queryField: 'product_id',
        columns: ['product_id', 'name', 'price', 'stock'],
        apiEndpoint: 'api/product_repair.php'
    },
    customer: {
        tableName: 'customers',
        action: 'update',
        queryField: 'customer_id',
        columns: ['customer_id', 'name', 'phone', 'address'],
        apiEndpoint: 'api/user_repair.php'
    },
    payment: {
        tableName: 'payments',
        action: 'update',
        queryField: 'payment_id',
        columns: ['payment_id', 'order_id', 'amount', 'method'],
        apiEndpoint: 'api/order_repair.php'
    },
    category: {
        tableName: 'categories',
        action: 'update',
        queryField: 'category_id',
        columns: ['category_id', 'name', 'parent_id', 'sort_order'],
        apiEndpoint: 'api/product_repair.php'
    },
    inventory: {
        tableName: 'inventory',
        action: 'update',
        queryField: 'inventory_id',
        columns: ['inventory_id', 'product_id', 'quantity', 'location'],
        apiEndpoint: 'api/product_repair.php'
    },
    supplier: {
        tableName: 'suppliers',
        action: 'update',
        queryField: 'supplier_id',
        columns: ['supplier_id', 'name', 'contact', 'email'],
        apiEndpoint: 'api/user_repair.php'
    },
    coupon: {
        tableName: 'coupons',
        action: 'update',
        queryField: 'coupon_id',
        columns: ['coupon_id', 'code', 'discount', 'expire_date'],
        apiEndpoint: 'api/order_repair.php'
    },
    review: {
        tableName: 'reviews',
        action: 'update',
        queryField: 'review_id',
        columns: ['review_id', 'product_id', 'user_id', 'rating'],
        apiEndpoint: 'api/product_repair.php'
    },
    shipping: {
        tableName: 'shipping',
        action: 'update',
        queryField: 'shipping_id',
        columns: ['shipping_id', 'order_id', 'address', 'status'],
        apiEndpoint: 'api/order_repair.php'
    },
    log: {
        tableName: 'logs',
        action: 'insert',
        queryField: 'log_id',
        columns: ['log_id', 'user_id', 'action', 'timestamp'],
        apiEndpoint: 'api/user_repair.php'
    },
    config: {
        tableName: 'configs',
        action: 'update',
        queryField: 'config_key',
        columns: ['config_key', 'config_value', 'description', 'type'],
        apiEndpoint: 'api/user_repair.php'
    },
    report: {
        tableName: 'reports',
        action: 'update',
        queryField: 'report_id',
        columns: ['report_id', 'title', 'data', 'created_at'],
        apiEndpoint: 'api/order_repair.php'
    },
    backup: {
        tableName: 'backups',
        action: 'insert',
        queryField: 'backup_id',
        columns: ['backup_id', 'filename', 'size', 'created_at'],
        apiEndpoint: 'api/user_repair.php'
    }
};

// 全局数据存储
let globalTableData = null;
let globalCSVData = null;
let batchResults = [];
let repairReason = ''; // 修复原因，全局保存

// 全局变量
let currentTab = 'node';
let isProcessing = false;
let pendingRepairAction = null; // 存储待执行的修复操作

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSharedInterface();
    updateTabConfiguration(currentTab);

    // 设置默认日期为今天
    document.getElementById('operationDate').value = new Date().toISOString().split('T')[0];

    // 监听标签页切换
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const tabId = event.target.getAttribute('data-bs-target').replace('#', '').replace('-pane', '');
            currentTab = tabId;
            updateTabConfiguration(tabId);
        });
    });
});

// 处理动作变化
function handleActionChange() {
    const action = document.getElementById('action').value;
    const updateFields = document.getElementById('updateFields');
    const deleteFields = document.getElementById('deleteFields');

    // 隐藏所有额外字段
    if (updateFields) updateFields.style.display = 'none';
    if (deleteFields) deleteFields.style.display = 'none';

    // 根据选择的动作显示对应字段
    if (action === '更新' && updateFields) {
        updateFields.style.display = 'block';
    } else if (action === '删除' && deleteFields) {
        deleteFields.style.display = 'block';
    }
}

// 初始化共享界面
function initializeSharedInterface() {
    generateSharedTableTemplate();
    updateCurrentTabIndicator(currentTab);
}

// 更新标签页配置
function updateTabConfiguration(tabId) {
    const config = tabConfigs[tabId];
    if (!config) return;

    // 清空字段选择
    clearFormFields();

    if (tabId === 'node') {
        // node操作标签页的特殊处理
        setupNodeConfiguration(config);
    } else {
        // 其他标签页的传统处理
        setupTraditionalConfiguration(config);
    }

    // 更新当前标签页指示器
    updateCurrentTabIndicator(tabId);

    // 切换标签页时更新表格模板
    generateSharedTableTemplate();
}

// 清空表单字段
function clearFormFields() {
    // 隐藏所有动态字段
    document.getElementById('updateFields').style.display = 'none';
    document.getElementById('deleteFields').style.display = 'none';

    // 清空字段值
    const fields = ['tableName', 'action', 'queryConditions', 'updateFieldsList', 'deleteConditions'];
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            if (field.tagName === 'SELECT') {
                field.selectedIndex = 0;
            } else {
                field.value = '';
            }
        }
    });
}

// 设置node操作配置
function setupNodeConfiguration(config) {
    // 显示表名下拉框并填充选项
    const tableNameSelect = document.getElementById('tableName');
    tableNameSelect.innerHTML = '<option value="">请选择表名</option>';
    config.tableOptions.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        tableNameSelect.appendChild(optionElement);
    });

    // 设置默认动作
    const actionSelect = document.getElementById('action');
    actionSelect.innerHTML = '<option value="">请选择动作</option>';
    config.supportOptions.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        actionSelect.appendChild(optionElement);
    });

    // 触发动作变化处理
    handleActionChange();
}

// 设置传统配置
function setupTraditionalConfiguration(config) {
    // 对于其他标签页，保持原有逻辑
    const tableNameSelect = document.getElementById('tableName');
    tableNameSelect.innerHTML = `<option value="${config.tableName}">${config.tableName}</option>`;
    tableNameSelect.value = config.tableName;

    document.getElementById('action').value = config.action;

    // 隐藏node特有的字段
    document.getElementById('updateFields').style.display = 'none';
    document.getElementById('deleteFields').style.display = 'none';
}

// 处理动作变化
function handleActionChange() {
    const action = document.getElementById('action').value;
    const updateFields = document.getElementById('updateFields');
    const deleteFields = document.getElementById('deleteFields');

    // 隐藏所有额外字段
    updateFields.style.display = 'none';
    deleteFields.style.display = 'none';

    // 根据动作显示对应字段
    if (action === '更新') {
        updateFields.style.display = 'block';
    } else if (action === '删除') {
        deleteFields.style.display = 'block';
    }
}

// 更新当前标签页指示器
function updateCurrentTabIndicator(tabId) {
    const indicator = document.getElementById('currentTabIndicator');
    const tabNames = {
        node: 'node操作', user: '用户', order: '订单', product: '商品', customer: '客户', payment: '支付',
        category: '分类', inventory: '库存', supplier: '供应商', coupon: '优惠券', review: '评价',
        shipping: '物流', log: '日志', config: '配置', report: '报表', backup: '备份'
    };

    if (indicator) {
        indicator.textContent = tabNames[tabId] || tabId;
    }
}

// 显示修复原因对话框
function showRepairReasonDialog(type) {
    // 如果已经有修复原因，直接执行
    if (repairReason.trim()) {
        if (type === 'quick') {
            startQuickRepair();
        } else if (type === 'batch') {
            startBatchRepair();
        }
        return;
    }

    // 创建对话框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>填写修复原因
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="repairReasonInput" class="form-label">修复原因</label>
                        <input type="text" class="form-control" id="repairReasonInput"
                               placeholder="请填写修复原因，格式如"修复上架编号_linweihong_0719""
                               value="${repairReason}">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        修复原因将在本次会话中保存，切换标签页不会丢失。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmRepairReason('${type}')">确认开始修复</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 聚焦到输入框
    modal.addEventListener('shown.bs.modal', function() {
        document.getElementById('repairReasonInput').focus();
    });

    // 清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 确认修复原因并开始修复
function confirmRepairReason(type) {
    const reasonInput = document.getElementById('repairReasonInput');
    const reason = reasonInput.value.trim();

    if (!reason) {
        showToast('请填写修复原因', 'warning');
        reasonInput.focus();
        return;
    }

    // 保存修复原因
    repairReason = reason;

    // 关闭对话框
    const modal = reasonInput.closest('.modal');
    const bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();

    // 执行对应的修复操作
    if (type === 'quick') {
        startQuickRepair();
    } else if (type === 'batch') {
        startBatchRepair();
    }
}

// 显示修复原因对话框
function showRepairReasonDialog(type) {
    pendingRepairAction = type;
    const modal = new bootstrap.Modal(document.getElementById('repairReasonModal'));

    // 清空表单
    document.getElementById('repairReason').value = '';
    document.getElementById('operatorName').value = '';
    document.getElementById('operationDate').value = new Date().toISOString().split('T')[0];

    modal.show();
}

// 确认修复操作
function confirmRepairAction() {
    const form = document.getElementById('repairReasonForm');
    const reason = document.getElementById('repairReason').value.trim();
    const operator = document.getElementById('operatorName').value.trim();
    const date = document.getElementById('operationDate').value;

    // 验证表单
    if (!reason || !operator || !date) {
        showToast('请填写完整的修复信息', 'error');
        return;
    }

    // 构建修复记录
    const repairRecord = {
        reason: reason,
        operator: operator,
        date: date,
        timestamp: new Date().toISOString(),
        type: pendingRepairAction
    };

    // 关闭对话框
    const modal = bootstrap.Modal.getInstance(document.getElementById('repairReasonModal'));
    modal.hide();

    // 执行对应的修复操作
    if (pendingRepairAction === 'quick') {
        startQuickRepair(repairRecord);
    } else if (pendingRepairAction === 'batch') {
        startBatchRepair(repairRecord);
    }

    pendingRepairAction = null;
}

// 生成共享表格模板
function generateSharedTableTemplate() {
    const table = document.getElementById('sharedQuickRepairTable');
    if (!table) return;

    // 总是根据当前标签页生成新模板
    const config = tabConfigs[currentTab];

    // 清空表格
    table.innerHTML = '';
    table.className = 'excel-table';

    // 创建表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    config.columns.forEach((column, index) => {
        const th = document.createElement('th');
        th.textContent = column;
        th.contentEditable = true;
        th.addEventListener('blur', function() {
            saveTableData();
        });
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');
    // 添加3行示例数据
    for (let i = 0; i < 3; i++) {
        addExcelTableRow(tbody, config.columns.length);
    }
    table.appendChild(tbody);

    // 添加粘贴事件监听
    addPasteEventListener(table);

    // 保存初始数据
    saveTableData();
}

// 添加Excel风格的表格行
function addExcelTableRow(tbody, columnCount) {
    const row = document.createElement('tr');

    for (let j = 0; j < columnCount; j++) {
        const td = document.createElement('td');
        td.className = 'editable';
        td.contentEditable = true;
        td.addEventListener('blur', function() {
            saveTableData();
        });
        row.appendChild(td);
    }

    // 添加删除按钮到最后一个单元格
    if (columnCount > 0) {
        const lastTd = row.lastElementChild;
        lastTd.style.position = 'relative';
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-row-btn';
        deleteBtn.innerHTML = '×';
        deleteBtn.title = '删除行';
        deleteBtn.onclick = function() {
            row.remove();
            saveTableData();
        };
        lastTd.appendChild(deleteBtn);
    }

    tbody.appendChild(row);
}

// 下载快速修复模板
function downloadTemplate() {
    const config = tabConfigs[currentTab];
    const templateData = [config.columns];

    // 添加示例数据行
    const exampleRow = config.columns.map((column, index) => {
        switch (column) {
            case 'user_id':
            case 'order_id':
            case 'product_id':
            case 'customer_id':
            case 'payment_id':
            case 'category_id':
            case 'inventory_id':
            case 'supplier_id':
            case 'coupon_id':
            case 'review_id':
            case 'shipping_id':
            case 'log_id':
            case 'report_id':
            case 'backup_id':
                return '1';
            case 'username':
            case 'name':
                return '示例名称';
            case 'email':
                return '<EMAIL>';
            case 'status':
                return 'active';
            case 'amount':
            case 'price':
                return '100.00';
            case 'phone':
                return '13800138000';
            case 'address':
                return '示例地址';
            case 'method':
                return 'credit_card';
            case 'quantity':
            case 'stock':
                return '10';
            case 'code':
                return 'EXAMPLE';
            case 'rating':
                return '5';
            default:
                return '示例数据';
        }
    });

    templateData.push(exampleRow);

    const csv = convertToCSV(templateData);
    const tabNames = {
        user: '用户', order: '订单', product: '商品', customer: '客户', payment: '支付',
        category: '分类', inventory: '库存', supplier: '供应商', coupon: '优惠券', review: '评价',
        shipping: '物流', log: '日志', config: '配置', report: '报表', backup: '备份'
    };

    const fileName = `${tabNames[currentTab] || currentTab}_template.csv`;
    downloadCSV(csv, fileName);
    showToast(`${tabNames[currentTab] || currentTab}数据模板已下载`, 'success');
}

// 下载大批量修复模板
function downloadBatchTemplate() {
    downloadTemplate(); // 使用相同的模板生成逻辑
}

// 保存表格数据到全局变量
function saveTableData() {
    const table = document.getElementById('sharedQuickRepairTable');
    if (table) {
        globalTableData = getTableData(table);
    }
}

// 恢复表格数据
function restoreTableData(table, data) {
    if (!data || data.length === 0) return;

    table.innerHTML = '';
    table.className = 'table table-bordered editable-table';

    // 创建表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    data[0].forEach((column, index) => {
        const th = document.createElement('th');
        th.innerHTML = `
            <input type="text" value="${column}" onchange="updateColumnHeader(this); saveTableData();" class="form-control form-control-sm">
            <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${index})" title="删除列">×</button>
        `;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');
    for (let i = 1; i < data.length; i++) {
        const row = document.createElement('tr');
        data[i].forEach(cellValue => {
            const td = document.createElement('td');
            td.innerHTML = `
                <input type="text" class="form-control form-control-sm" value="${escapeHtml(cellValue)}" placeholder="输入数据" onchange="saveTableData()">
                <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
            `;
            row.appendChild(td);
        });
        tbody.appendChild(row);
    }
    table.appendChild(tbody);

    // 添加粘贴事件监听
    addPasteEventListener(table);
}

// Toast 消息函数
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) return;

    const toastId = 'toast-' + Date.now();
    const iconMap = {
        'success': 'fas fa-check-circle text-success',
        'error': 'fas fa-exclamation-circle text-danger',
        'warning': 'fas fa-exclamation-triangle text-warning',
        'info': 'fas fa-info-circle text-info'
    };

    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="${iconMap[type] || iconMap.info} me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <small class="text-muted">刚刚</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        delay: duration
    });

    toast.show();

    // 自动清理DOM
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 添加表格行到tbody
function addTableRowToBody(tbody, columnCount) {
    const row = document.createElement('tr');
    for (let j = 0; j < columnCount; j++) {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据" onchange="saveTableData()">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    }
    tbody.appendChild(row);
}

// 添加表格行
function addTableRow(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const columnCount = table.querySelector('thead tr').children.length;
    addTableRowToBody(tbody, columnCount);
    saveTableData();
}

// 添加表格列
function addTableColumn(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    // 添加表头
    const headerRow = table.querySelector('thead tr');
    const th = document.createElement('th');
    const columnIndex = headerRow.children.length;
    th.innerHTML = `
        <input type="text" value="新列${columnIndex + 1}" onchange="updateColumnHeader(this)" class="form-control form-control-sm">
        <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${columnIndex})" title="删除列">×</button>
    `;
    headerRow.appendChild(th);

    // 为每一行添加新列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    });
}

// 删除行
function deleteRow(button) {
    const row = button.closest('tr');
    row.remove();
}

// 删除列
function deleteColumn(button, columnIndex) {
    const table = button.closest('table');

    // 删除表头
    const headerRow = table.querySelector('thead tr');
    if (headerRow.children.length <= 1) {
        alert('至少需要保留一列');
        return;
    }
    headerRow.children[columnIndex].remove();

    // 删除每行对应的列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (row.children[columnIndex]) {
            row.children[columnIndex].remove();
        }
    });
}

// 清空表格
function clearTable(tableId) {
    // 创建确认对话框
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认清空</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    确定要清空表格吗？此操作不可撤销。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmClear">确定清空</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();

    // 确认清空
    document.getElementById('confirmClear').onclick = function() {
        const table = document.getElementById(tableId);
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        modal.hide();
        showToast('表格已清空', 'info');
    };

    // 清理DOM
    confirmModal.addEventListener('hidden.bs.modal', function() {
        confirmModal.remove();
    });
}

// 导出表格
function exportTable(tableId) {
    const table = document.getElementById(tableId);
    const data = getTableData(table);

    if (data.length === 0) {
        showToast('表格为空，无法导出', 'warning');
        return;
    }

    const csv = convertToCSV(data);
    downloadCSV(csv, `${tableId}_export.csv`);
    showToast('表格导出成功', 'success');
}

// 获取表格数据
function getTableData(table) {
    const data = [];

    // 获取表头
    const headers = [];
    const headerInputs = table.querySelectorAll('thead th');
    headerInputs.forEach(input => {
        headers.push(input.textContent);
    });
    data.push(headers);

    // 获取数据行
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = [];
        const inputs = row.querySelectorAll('td');
        inputs.forEach(input => {
            rowData.push(input.textContent);
        });
        if (rowData.some(cell => cell.trim() !== '')) {
            data.push(rowData);
        }
    });

    return data;
}

// 转换为CSV格式
function convertToCSV(data) {
    return data.map(row =>
        row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
}

// 下载CSV文件
function downloadCSV(csv, filename) {
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 处理CSV文件上传
function handleCSVFile(event) {
    const file = event.target.files[0];
    const startBtn = document.getElementById('batchStartBtn');
    const exportBtn = document.getElementById('exportResultsBtn');

    if (!file) {
        // 如果没有选择文件，禁用按钮
        if (startBtn) startBtn.disabled = true;
        if (exportBtn) exportBtn.disabled = true;
        globalCSVData = null;
        batchResults = [];
        return;
    }

    // 强制重新读取文件，即使文件名相同
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        globalCSVData = parseCSV(csv);
        batchResults = []; // 重置结果

        if (globalCSVData.length > 0) {
            displayCSVPreview(globalCSVData);
            if (startBtn) startBtn.disabled = false;
            if (exportBtn) exportBtn.disabled = true; // 重新上传文件时禁用导出
            showToast(`成功解析CSV文件，共 ${globalCSVData.length - 1} 行数据`, 'success');
        } else {
            if (startBtn) startBtn.disabled = true;
            if (exportBtn) exportBtn.disabled = true;
            showToast('CSV文件解析失败或文件为空', 'error');
        }
    };

    reader.onerror = function() {
        showToast('文件读取失败', 'error');
        if (startBtn) startBtn.disabled = true;
        if (exportBtn) exportBtn.disabled = true;
    };

    reader.readAsText(file, 'UTF-8');

    // 重置文件输入的值，确保相同文件也能触发change事件
    setTimeout(() => {
        event.target.value = '';
    }, 100);
}

// 解析CSV数据
function parseCSV(csv) {
    const lines = csv.split('\n');
    const result = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            const row = [];
            let current = '';
            let inQuotes = false;

            for (let j = 0; j < line.length; j++) {
                const char = line[j];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    row.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            row.push(current.trim());
            result.push(row);
        }
    }

    return result;
}

// 显示CSV预览
function displayCSVPreview(data) {
    const previewDiv = document.getElementById('csvPreview');
    const previewTable = document.getElementById('csvPreviewTable');
    const previewInfo = document.getElementById('csvPreviewInfo');

    if (!previewDiv || !previewTable) return;

    previewTable.innerHTML = '';

    // 更新预览信息
    const totalRows = data.length - 1; // 减去表头
    const totalCols = data[0] ? data[0].length : 0;
    const previewRows = Math.min(10, totalRows);

    if (previewInfo) {
        previewInfo.innerHTML = `
            <span class="me-3">总计: ${totalRows} 行 × ${totalCols} 列</span>
            <span>预览: ${previewRows} 行</span>
        `;
    }

    // 显示前10行数据
    const previewData = data.slice(0, 11); // 包含表头

    previewData.forEach((row, index) => {
        const tr = document.createElement('tr');
        if (index === 0) {
            // 表头
            row.forEach(cell => {
                const th = document.createElement('th');
                th.textContent = cell || '';
                th.title = cell || '';
                tr.appendChild(th);
            });
        } else {
            // 数据行
            row.forEach(cell => {
                const td = document.createElement('td');
                td.textContent = cell || '';
                td.title = cell || '';
                tr.appendChild(td);
            });
        }
        previewTable.appendChild(tr);
    });

    previewDiv.style.display = 'block';
}

// 添加粘贴事件监听
function addPasteEventListener(table) {
    table.addEventListener('paste', function(e) {
        e.preventDefault();

        // 获取粘贴的数据
        let pastedData = '';
        if (e.clipboardData) {
            pastedData = e.clipboardData.getData('text');
        } else if (window.clipboardData) {
            pastedData = window.clipboardData.getData('Text');
        }

        if (!pastedData.trim()) return;

        // 解析粘贴的数据（支持Excel复制的制表符分隔格式）
        const rows = pastedData.trim().split('\n');
        const parsedData = rows.map(row => {
            // 处理制表符分隔的数据（Excel格式）
            return row.split('\t').map(cell => cell.trim());
        });

        if (parsedData.length === 0) return;

        // 确定需要的列数
        const maxColumns = Math.max(...parsedData.map(row => row.length));
        const currentColumns = table.querySelector('thead tr').children.length;

        // 如果需要更多列，自动添加
        if (maxColumns > currentColumns) {
            for (let i = currentColumns; i < maxColumns; i++) {
                addTableColumnInternal(table, `列${i + 1}`);
            }
        }

        // 清空现有数据行
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        // 添加粘贴的数据
        parsedData.forEach(rowData => {
            const row = document.createElement('tr');

            for (let i = 0; i < maxColumns; i++) {
                const td = document.createElement('td');
                const value = rowData[i] || '';
                td.innerHTML = `
                    <input type="text" class="form-control form-control-sm" value="${escapeHtml(value)}" placeholder="输入数据">
                    <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
                `;
                row.appendChild(td);
            }

            tbody.appendChild(row);
        });

        // 如果没有数据行，至少添加一行空行
        if (parsedData.length === 0) {
            addTableRowToBody(tbody, maxColumns);
        }

        showToast(`成功粘贴 ${parsedData.length} 行 ${maxColumns} 列数据`, 'success');
    });
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 内部添加列函数（不触发事件）
function addTableColumnInternal(table, columnName) {
    // 添加表头
    const headerRow = table.querySelector('thead tr');
    const th = document.createElement('th');
    const columnIndex = headerRow.children.length;
    th.innerHTML = `
        <input type="text" value="${columnName}" onchange="updateColumnHeader(this)" class="form-control form-control-sm">
        <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${columnIndex})" title="删除列">×</button>
    `;
    headerRow.appendChild(th);

    // 为每一行添加新列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    });
}

// 更新列标题
function updateColumnHeader(input) {
    // 列标题更新逻辑已在input的onchange中处理
}

// 开始快速修复
function startQuickRepair(repairRecord) {
    if (isProcessing) {
        showToast('正在处理中，请等待完成', 'warning');
        return;
    }

    const table = document.getElementById('sharedQuickRepairTable');
    const data = getTableData(table);

    if (data.length <= 1) {
        showToast('请先添加数据', 'warning');
        return;
    }

    // 验证必填字段
    if (!validateTableData(data)) {
        return;
    }

    const threadsInput = document.getElementById('quickThreads');
    const threads = parseInt(threadsInput.value) || 10;

    // 添加结果列
    addResultColumn(table);

    // 显示进度条
    showQuickRepairProgress(data.length - 1); // 减1因为第一行是表头

    // 记录修复信息
    console.log('快速修复开始', repairRecord);

    
    const tableName = document.getElementById('tableName').value;
    const action = document.getElementById('action').value;

    if (!tableName) {
        return { success: false, message: '请选择表名' };
    }
    
    if (!action) {
        return { success: false, message: '请选择动作' };
    }

    isProcessing = true;
    processTableData(data, threads, repairRecord);
}

// 获取Excel风格表格数据
function getExcelTableData(table) {
    const data = [];

    // 获取表头
    const headers = [];
    const headerCells = table.querySelectorAll('thead th');
    headerCells.forEach(th => {
        headers.push(th.textContent.trim());
    });
    data.push(headers);

    // 获取数据行
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = [];
        const cells = row.querySelectorAll('td.editable');
        cells.forEach(td => {
            rowData.push(td.textContent.trim());
        });
        data.push(rowData);
    });

    return data;
}

// 导出快速修复结果
function exportQuickResults() {
    const table = document.getElementById('sharedQuickRepairTable');
    const data = getExcelTableData(table);

    if (data.length <= 1) {
        showToast('没有可导出的结果数据', 'warning');
        return;
    }

    const csv = convertToCSV(data);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    downloadCSV(csv, `quick_repair_results_${timestamp}.csv`);
    showToast('快速修复结果已导出', 'success');
}

// 显示快速修复进度
function showQuickRepairProgress(totalCount) {
    const progressDiv = document.getElementById('quickProgress');
    const totalCountSpan = document.getElementById('quickTotalCount');
    const processedCountSpan = document.getElementById('quickProcessedCount');
    const successCountSpan = document.getElementById('quickSuccessCount');
    const errorCountSpan = document.getElementById('quickErrorCount');

    if (progressDiv && totalCountSpan) {
        progressDiv.style.display = 'block';
        totalCountSpan.textContent = totalCount;
        processedCountSpan.textContent = '0';
        successCountSpan.textContent = '0';
        errorCountSpan.textContent = '0';
    }
}

// 更新快速修复进度
function updateQuickRepairProgress(processedCount, totalCount, successCount, errorCount, startTime) {
    const progressBar = document.getElementById('quickProgressBar');
    const processedCountSpan = document.getElementById('quickProcessedCount');
    const elapsedTimeSpan = document.getElementById('quickElapsedTime');
    const successCountSpan = document.getElementById('quickSuccessCount');
    const errorCountSpan = document.getElementById('quickErrorCount');

    if (!progressBar) return;

    const percentage = Math.round((processedCount / totalCount) * 100);
    const elapsedTime = (Date.now() - startTime) / 1000;

    // 更新进度条
    progressBar.style.width = `${percentage}%`;
    progressBar.textContent = `${percentage}%`;

    // 更新计数
    if (processedCountSpan) processedCountSpan.textContent = processedCount;
    if (successCountSpan) successCountSpan.textContent = successCount;
    if (errorCountSpan) errorCountSpan.textContent = errorCount;

    // 更新已用时间
    if (elapsedTimeSpan) {
        elapsedTimeSpan.textContent = `${elapsedTime.toFixed(2)}秒`;
    }
}

// 验证表格数据
function validateTableData(data) {
    const config = tabConfigs[currentTab];
    const headers = data[0];

    // 检查是否包含查询字段
    const queryConditions = document.getElementById('queryConditions').value;
    if(!queryConditions){
        return true;
    }

    let errorMessage = '';
    queryConditions.split(",").forEach(field => {
        if (!headers.includes(field)) {
            errorMessage += field + ", ";
        }
    });
    if(errorMessage){
        errorMessage.trim(", ");
        showToast(`表格必须包含查询字段: ${errorMessage}`, 'error');
        return false;
    }

    // 检查数据行是否为空
    for (let i = 1; i < data.length; i++) {
        const row = data[i];
        if (row.every(cell => !cell.trim())) {
            continue;
        }

        queryConditions.split(",").forEach(field => {
            const queryFieldIndex = headers.indexOf(field);
            if (!row[queryFieldIndex] || !row[queryFieldIndex].trim()) {
                showToast(`第${i}行的查询字段不能为空`, 'error');
                return false;
            }
        });
    }

    return true;
}

// 添加结果列
function addResultColumn(table) {
    // 检查是否已经有结果列
    const headerRow = table.querySelector('thead tr');
    const lastHeader = headerRow.lastElementChild;

    if (lastHeader.textContent !== 'result') {
        // 添加结果列表头
        const th = document.createElement('th');
        th.innerHTML = `<th readonly>result</th>`;
        headerRow.appendChild(th);

        // 为每一行添加结果列
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const td = document.createElement('td');
            td.innerHTML = '<span class="result-pending">等待中...</span>';
            row.appendChild(td);
        });
    }
}

// 处理表格数据
async function processTableData(data, threads, repairRecord) {
    const headers = data[0];
    const rows = data.slice(1).filter(row => row.some(cell => cell.trim()));

    const startTime = Date.now();
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 分组处理
    const chunks = chunkArray(rows, threads);

    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const promises = chunk.map((row, index) =>
            processRow(row, headers, processedCount + index, repairRecord)
        );

        const results = await Promise.all(promises);

        // 统计结果
        results.forEach(result => {
            if (result && result.success) {
                successCount++;
            } else {
                errorCount++;
            }
        });

        processedCount += chunk.length;

        // 更新进度
        updateQuickRepairProgress(processedCount, rows.length, successCount, errorCount, startTime);
    }

    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;

    // 启用导出按钮
    const exportBtn = document.getElementById('exportQuickResultsBtn');
    if (exportBtn) {
        exportBtn.disabled = false;
    }

    isProcessing = false;
    showToast(`快速修复完成！成功: ${successCount}, 失败: ${errorCount}, 耗时: ${formatTime(totalTime)}`, 'success', 5000);
}

// 处理单行数据
async function processRow(row, headers, rowIndex, repairRecord) {
    try {
        // 构建请求参数
        const params = {};
        headers.forEach((header, index) => {
            if (header !== 'result') {
                params[header] = row[index] || '';
            }
        });

        // 添加修复记录信息
        params.repair_record = repairRecord;

        let result;

        if (currentTab === 'node') {
            // node操作的特殊处理逻辑
            result = await processNodeOperation(params);
        } else {
            // 其他标签页的通用处理
            result = await processGenericOperation(params);
        }

        // 更新结果
        updateRowResult(rowIndex, result.success ? '成功' : result.message || '失败', result.success);

        return result;

    } catch (error) {
        updateRowResult(rowIndex, '网络错误', false);
        return { success: false, message: '网络错误' };
    }
}

// 处理通用操作
async function processGenericOperation(params) {
    const config = tabConfigs[currentTab];

    // 添加配置参数
    params.table_name = document.getElementById('tableName').value;
    params.action = document.getElementById('action').value;
    params.query_field = document.getElementById('queryField').value;
    params.repair_reason = repairReason;

    return await makeRequest(config.apiEndpoint, 'POST', params);
}

// 统一的请求函数
async function makeRequest(url, method, params) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (method !== 'GET') {
            options.body = JSON.stringify(params);
        } else if (params) {
            // GET请求将参数添加到URL
            const urlParams = new URLSearchParams(params);
            url += '?' + urlParams.toString();
        }

        const response = await fetch(url, options);
        return await response.json();
    } catch (error) {
        return { success: false, message: '网络错误: ' + error.message };
    }
}

// 处理node操作
async function processNodeOperation(params) {
    const currentTab = 'node';
    const tableName = document.getElementById('tableName').value;
    const action = document.getElementById('action').value;

    if (!tableName) {
        return { success: false, message: '请选择表名' };
    }
    
    if (!action) {
        return { success: false, message: '请选择动作' };
    }

    const config = tabConfigs.node;
    let nodeGateway = '';
    for(let service in config.apiEndpoints) {
        if (config.apiEndpoints[service].tableLists.includes(tableName)) {
            nodeGateway = config.apiEndpoints[service].nodeGateway;
            break;
        }
    }
    if (!nodeGateway) {
        return { success: false, message: '未配置接口' };
    }

    try {
        switch (action) {
            case 'create':
                const url = `${nodeGateway}${tableName}`; 
                return await makeRequest(url, 'POST', params);
            case 'delete':
                return await handleUpdateOperation(params, tableConfig);
            case 'update':
                return await handleDeleteOperation(params, tableConfig);
            default:
                return { success: false, message: '不支持的操作类型' };
        }
    } catch (error) {
        return { success: false, message: error.message };
    }
}

// 处理新增操作
async function handleCreateOperation(url, params, tableConfig) {
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(params)
    });

    return await response.json();
}

// 处理更新操作
async function handleUpdateOperation(params, tableConfig) {
    // 先查询数据
    const queryConditions = document.getElementById('queryConditions').value;
    const updateFields = document.getElementById('updateFieldsList').value;

    if (!queryConditions || !updateFields) {
        return { success: false, message: '请填写查询条件和更新字段' };
    }

    // 构建查询参数
    const queryParams = {
        ...params,
        query_conditions: queryConditions.split(',').map(s => s.trim()),
        update_fields: updateFields.split(',').map(s => s.trim())
    };

    // 先查询
    const queryResponse = await fetch(tableConfig.endpoints.query, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryParams)
    });

    const queryResult = await queryResponse.json();

    if (!queryResult.success) {
        return { success: false, message: '查询失败: ' + queryResult.message };
    }

    // 组装更新参数
    const updateParams = {
        ...params,
        query_data: queryResult.data,
        update_fields: updateFields.split(',').map(s => s.trim())
    };

    // 发送更新请求
    const updateResponse = await fetch(tableConfig.endpoints.update, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateParams)
    });

    return await updateResponse.json();
}

// 处理删除操作
async function handleDeleteOperation(params, tableConfig) {
    const deleteConditions = document.getElementById('deleteConditions').value;

    if (!deleteConditions) {
        return { success: false, message: '请填写删除条件' };
    }

    const deleteParams = {
        ...params,
        delete_conditions: deleteConditions
    };

    const response = await fetch(tableConfig.endpoints.delete, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(deleteParams)
    });

    return await response.json();
}

// 处理通用操作（其他标签页）
async function processGenericOperation(params) {
    const config = tabConfigs[currentTab];

    // 添加配置参数
    params.table_name = document.getElementById('tableName').value;
    params.action = document.getElementById('action').value;
    params.query_field = document.getElementById('queryField').value;

    // 发送请求
    const response = await fetch(config.apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(params)
    });

    return await response.json();
}

// 更新行结果
function updateRowResult(rowIndex, message, success) {
    const table = document.getElementById('sharedQuickRepairTable');
    const rows = table.querySelectorAll('tbody tr');

    if (rows[rowIndex]) {
        const resultCell = rows[rowIndex].lastElementChild;
        const className = success ? 'result-success' : 'result-error';
        resultCell.innerHTML = `<span class="${className}">${message}</span>`;
    }
}

// 数组分块
function chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

// 开始大批量修复
function startBatchRepair(repairRecord) {
    if (isProcessing) {
        showToast('正在处理中，请等待完成', 'warning');
        return;
    }

    if (!globalCSVData || globalCSVData.length <= 1) {
        showToast('请先上传CSV文件', 'warning');
        return;
    }

    // 验证CSV数据
    if (!validateTableData(globalCSVData)) {
        return;
    }

    const threadsInput = document.getElementById('batchThreads');
    const threads = parseInt(threadsInput.value) || 50;

    // 记录修复信息
    console.log('大批量修复开始', repairRecord);

    isProcessing = true;
    batchResults = []; // 重置结果
    processBatchData(globalCSVData, threads, repairRecord);
}

// 导出批量处理结果
function exportBatchResults() {
    if (!batchResults || batchResults.length === 0) {
        showToast('没有可导出的结果数据', 'warning');
        return;
    }

    // 构建导出数据
    const exportData = [];

    // 添加表头
    if (globalCSVData && globalCSVData.length > 0) {
        const headers = [...globalCSVData[0], 'processing_result', 'processing_message'];
        exportData.push(headers);

        // 添加数据行
        for (let i = 1; i < globalCSVData.length; i++) {
            const originalRow = globalCSVData[i];
            const result = batchResults[i - 1]; // batchResults索引比globalCSVData少1

            if (result) {
                const resultRow = [
                    ...originalRow,
                    result.success ? '成功' : '失败',
                    result.message || ''
                ];
                exportData.push(resultRow);
            } else {
                const resultRow = [
                    ...originalRow,
                    '未处理',
                    '数据未处理'
                ];
                exportData.push(resultRow);
            }
        }
    }

    // 转换为CSV并下载
    const csv = convertToCSV(exportData);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    downloadCSV(csv, `batch_repair_results_${timestamp}.csv`);
    showToast('处理结果已导出', 'success');
}

// 处理批量数据
async function processBatchData(data, threads, repairRecord) {
    const headers = data[0];
    const rows = data.slice(1).filter(row => row.some(cell => cell.trim()));

    // 显示进度条
    showBatchProgress(rows.length);

    const startTime = Date.now();
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 分组处理
    const chunks = chunkArray(rows, threads);

    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const promises = chunk.map(row =>
            processBatchRow(row, headers, repairRecord)
        );

        const results = await Promise.all(promises);

        // 保存结果并统计
        results.forEach(result => {
            batchResults.push(result);
            if (result.success) {
                successCount++;
            } else {
                errorCount++;
            }
        });

        processedCount += chunk.length;

        // 更新进度
        updateBatchProgress(processedCount, rows.length, startTime);
    }

    // 完成处理
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;

    document.getElementById('totalTime').textContent = formatTime(totalTime);

    // 显示处理结果摘要
    showBatchSummary(successCount, errorCount, rows.length, totalTime);

    // 启用导出按钮
    const exportBtn = document.getElementById('exportResultsBtn');
    if (exportBtn) {
        exportBtn.disabled = false;
    }

    isProcessing = false;
    showToast(`大批量修复完成！成功: ${successCount}, 失败: ${errorCount}, 总耗时: ${formatTime(totalTime)}`, 'success', 5000);
}

// 显示批量处理结果摘要
function showBatchSummary(successCount, errorCount, totalCount, totalTime) {
    const summaryDiv = document.getElementById('batchSummary');
    const summaryAlert = document.getElementById('summaryAlert');

    if (!summaryDiv || !summaryAlert) return;

    const isAllSuccess = errorCount === 0;
    const isPartialSuccess = successCount > 0 && errorCount > 0;

    let alertClass, icon, title, message;

    if (isAllSuccess) {
        alertClass = 'alert-success';
        icon = 'fas fa-check-circle';
        title = '全部成功';
        message = `所有 ${totalCount} 条数据处理成功`;
    } else if (isPartialSuccess) {
        alertClass = 'alert-warning';
        icon = 'fas fa-exclamation-triangle';
        title = '部分失败';
        message = `${totalCount} 条数据中，${successCount} 条成功，${errorCount} 条失败`;
    } else {
        alertClass = 'alert-danger';
        icon = 'fas fa-times-circle';
        title = '处理失败';
        message = `${totalCount} 条数据处理失败`;
    }

    summaryAlert.className = `alert ${alertClass}`;
    summaryAlert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <div>
                <strong>${title}</strong><br>
                <small>${message}，总耗时: ${formatTime(totalTime)}</small>
            </div>
        </div>
    `;

    summaryDiv.style.display = 'block';
}

// 处理批量单行数据
async function processBatchRow(row, headers, repairRecord) {
    try {
        // 构建请求参数
        const params = {};
        headers.forEach((header, index) => {
            params[header] = row[index] || '';
        });

        // 添加修复记录信息
        params.repair_record = repairRecord;

        let result;

        if (currentTab === 'node') {
            // node操作的特殊处理逻辑
            result = await processNodeOperation(params);
        } else {
            // 其他标签页的通用处理
            result = await processGenericOperation(params);
        }

        return result;

    } catch (error) {
        return { success: false, message: '网络错误: ' + error.message };
    }
}

// 显示批量进度
function showBatchProgress(totalCount) {
    const progressDiv = document.getElementById('batchProgress');
    const totalCountSpan = document.getElementById('totalCount');

    if (progressDiv && totalCountSpan) {
        progressDiv.style.display = 'block';
        totalCountSpan.textContent = totalCount;
    }
}

// 更新批量进度
function updateBatchProgress(processedCount, totalCount, startTime) {
    const progressBar = document.getElementById('progressBar');
    const processedCountSpan = document.getElementById('processedCount');
    const elapsedTimeSpan = document.getElementById('elapsedTime');
    const remainingTimeSpan = document.getElementById('remainingTime');

    if (!progressBar) return;

    const percentage = Math.round((processedCount / totalCount) * 100);
    const elapsedTime = (Date.now() - startTime) / 1000;

    // 更新进度条
    progressBar.style.width = `${percentage}%`;
    progressBar.textContent = `${percentage}%`;

    // 更新计数
    if (processedCountSpan) {
        processedCountSpan.textContent = processedCount;
    }

    // 更新已用时间
    if (elapsedTimeSpan) {
        elapsedTimeSpan.textContent = `${elapsedTime.toFixed(2)}秒`;
    }

    // 计算预计剩余时间
    if (remainingTimeSpan && processedCount > 0) {
        const avgTimePerItem = elapsedTime / processedCount;
        const remainingItems = totalCount - processedCount;
        const remainingTime = avgTimePerItem * remainingItems;

        remainingTimeSpan.textContent = formatTime(remainingTime);
    }
}

// 格式化时间
function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(2)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${hours}时${minutes}分${remainingSeconds}秒`;
    }
}

// 全局函数，供HTML调用
window.handleCSVFile = handleCSVFile;
window.startQuickRepair = startQuickRepair;
window.startBatchRepair = startBatchRepair;
window.exportBatchResults = exportBatchResults;
window.exportQuickResults = exportQuickResults;
window.downloadTemplate = downloadTemplate;
window.downloadBatchTemplate = downloadBatchTemplate;
window.showRepairReasonDialog = showRepairReasonDialog;
window.confirmRepairReason = confirmRepairReason;
window.handleActionChange = handleActionChange;
window.exportQuickResults = exportQuickResults;
window.addTableColumn = addTableColumn;
window.addTableRow = addTableRow;
window.clearTable = clearTable;
window.handleActionChange = handleActionChange;
window.showRepairReasonDialog = showRepairReasonDialog;
window.confirmRepairAction = confirmRepairAction;

// 导出快速修复结果
function exportQuickResults() {
    const table = document.getElementById('sharedQuickRepairTable');
    if (!table) {
        showToast('没有找到表格数据', 'warning');
        return;
    }

    const data = getTableData(table);
    if (!data || data.length === 0) {
        showToast('表格中没有数据可导出', 'warning');
        return;
    }

    const csv = convertToCSV(data);
    const fileName = `quick_repair_results_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.csv`;
    downloadCSV(csv, fileName);
    showToast('快速修复结果已导出', 'success');
}

// 添加表格列
function addTableColumn(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const thead = table.querySelector('thead tr');
    const tbody = table.querySelector('tbody');

    if (thead) {
        const th = document.createElement('th');
        th.textContent = '新列';
        th.contentEditable = true;
        th.addEventListener('blur', saveTableData);
        thead.appendChild(th);
    }

    if (tbody) {
        const rows = tbody.querySelectorAll('tr');
        rows.forEach(row => {
            const td = document.createElement('td');
            td.className = 'editable';
            td.contentEditable = true;
            td.addEventListener('blur', saveTableData);
            row.appendChild(td);
        });
    }

    saveTableData();
}

// 添加表格行
function addTableRow(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const thead = table.querySelector('thead');

    if (tbody && thead) {
        const columnCount = thead.querySelectorAll('th').length;
        addExcelTableRow(tbody, columnCount);
        saveTableData();
    }
}

// 清空表格
function clearTable(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    if (tbody) {
        tbody.innerHTML = '';
        // 添加一行空数据
        const thead = table.querySelector('thead');
        if (thead) {
            const columnCount = thead.querySelectorAll('th').length;
            addExcelTableRow(tbody, columnCount);
        }
        saveTableData();
    }
}
