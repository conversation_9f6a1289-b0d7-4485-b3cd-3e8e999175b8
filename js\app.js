// 全局变量
let csvData = [];
let currentTab = 'user';
let isProcessing = false;

// 标签页配置
const tabConfigs = {
    user: {
        tableName: 'users',
        action: 'update',
        queryField: 'user_id',
        columns: ['user_id', 'username', 'email', 'status'],
        apiEndpoint: 'api/user_repair.php'
    },
    order: {
        tableName: 'orders',
        action: 'update',
        queryField: 'order_id',
        columns: ['order_id', 'user_id', 'amount', 'status'],
        apiEndpoint: 'api/order_repair.php'
    },
    product: {
        tableName: 'products',
        action: 'update',
        queryField: 'product_id',
        columns: ['product_id', 'name', 'price', 'stock'],
        apiEndpoint: 'api/product_repair.php'
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    generateTableTemplate('quickRepairTable', currentTab);
    
    // 监听标签页切换
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const tabId = event.target.getAttribute('data-bs-target').replace('#', '').replace('-pane', '');
            currentTab = tabId;
            generateTabContent(tabId);
        });
    });
});

// 初始化标签页
function initializeTabs() {
    // 为每个标签页生成内容
    Object.keys(tabConfigs).forEach(tabId => {
        if (tabId !== 'user') {
            generateTabContent(tabId);
        }
    });
}

// 生成标签页内容
function generateTabContent(tabId) {
    const config = tabConfigs[tabId];
    const pane = document.getElementById(`${tabId}-pane`);
    
    if (!pane || pane.innerHTML.trim() !== '') return;
    
    pane.innerHTML = `
        <div class="row mt-3">
            <div class="col-12">
                <!-- 参数配置表单 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> 参数配置</h5>
                    </div>
                    <div class="card-body">
                        <form id="${tabId}ConfigForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="${tabId}TableName" class="form-label">表名</label>
                                    <input type="text" class="form-control" id="${tabId}TableName" value="${config.tableName}" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="${tabId}Action" class="form-label">更新动作</label>
                                    <select class="form-select" id="${tabId}Action" required>
                                        <option value="update" ${config.action === 'update' ? 'selected' : ''}>更新</option>
                                        <option value="insert" ${config.action === 'insert' ? 'selected' : ''}>插入</option>
                                        <option value="delete" ${config.action === 'delete' ? 'selected' : ''}>删除</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="${tabId}QueryField" class="form-label">查询字段</label>
                                    <input type="text" class="form-control" id="${tabId}QueryField" value="${config.queryField}" required>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速修复功能 -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-table"></i> 快速修复功能</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableRow('${tabId}QuickRepairTable')">
                                <i class="fas fa-plus"></i> 添加行
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableColumn('${tabId}QuickRepairTable')">
                                <i class="fas fa-plus"></i> 添加列
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearTable('${tabId}QuickRepairTable')">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportTable('${tabId}QuickRepairTable')">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered editable-table" id="${tabId}QuickRepairTable">
                                <!-- 表格内容将通过JavaScript动态生成 -->
                            </table>
                        </div>
                        <div class="d-flex align-items-center mt-3">
                            <label for="${tabId}QuickThreads" class="form-label me-2">线程数:</label>
                            <input type="number" class="form-control me-3" id="${tabId}QuickThreads" value="10" min="1" max="100" style="width: 100px;">
                            <button type="button" class="btn btn-primary" onclick="startQuickRepair('${tabId}')">
                                <i class="fas fa-play"></i> 开始修复
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大批量修复功能 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-csv"></i> 大批量修复功能</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="${tabId}CsvFile" class="form-label">选择CSV文件</label>
                            <input type="file" class="form-control" id="${tabId}CsvFile" accept=".csv" onchange="handleCSVFile(event, '${tabId}')">
                        </div>
                        <div id="${tabId}CsvPreview" class="mb-3" style="display: none;">
                            <h6>CSV预览:</h6>
                            <div class="table-responsive" style="max-height: 200px;">
                                <table class="table table-sm table-bordered" id="${tabId}CsvPreviewTable">
                                </table>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <label for="${tabId}BatchThreads" class="form-label me-2">线程数:</label>
                            <input type="number" class="form-control me-3" id="${tabId}BatchThreads" value="50" min="1" max="200" style="width: 100px;">
                            <button type="button" class="btn btn-primary" onclick="startBatchRepair('${tabId}')" disabled id="${tabId}BatchStartBtn">
                                <i class="fas fa-play"></i> 开始修复
                            </button>
                        </div>
                        <!-- 进度显示 -->
                        <div id="${tabId}BatchProgress" class="mt-3" style="display: none;">
                            <div class="progress mb-2">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="${tabId}ProgressBar">0%</div>
                            </div>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <small class="text-muted">已处理/总数</small><br>
                                    <span id="${tabId}ProcessedCount">0</span> / <span id="${tabId}TotalCount">0</span>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">处理时长</small><br>
                                    <span id="${tabId}ElapsedTime">0.00秒</span>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">预计剩余</small><br>
                                    <span id="${tabId}RemainingTime">--</span>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">总耗时</small><br>
                                    <span id="${tabId}TotalTime">--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 生成对应的表格模板
    generateTableTemplate(`${tabId}QuickRepairTable`, tabId);
}

// 生成表格模板
function generateTableTemplate(tableId, tabType) {
    const config = tabConfigs[tabType];
    const table = document.getElementById(tableId);
    if (!table) return;

    // 清空表格
    table.innerHTML = '';

    // 添加表格类名
    table.className = 'table table-bordered editable-table';

    // 创建表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    config.columns.forEach((column, index) => {
        const th = document.createElement('th');
        th.innerHTML = `
            <input type="text" value="${column}" onchange="updateColumnHeader(this)" class="form-control form-control-sm">
            <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${index})" title="删除列">×</button>
        `;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');
    // 添加3行示例数据
    for (let i = 0; i < 3; i++) {
        addTableRowToBody(tbody, config.columns.length);
    }
    table.appendChild(tbody);

    // 添加粘贴事件监听
    addPasteEventListener(table);
}

// 添加表格行到tbody
function addTableRowToBody(tbody, columnCount) {
    const row = document.createElement('tr');
    for (let j = 0; j < columnCount; j++) {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    }
    tbody.appendChild(row);
}

// 添加表格行
function addTableRow(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const columnCount = table.querySelector('thead tr').children.length;
    addTableRowToBody(tbody, columnCount);
}

// 添加表格列
function addTableColumn(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    // 添加表头
    const headerRow = table.querySelector('thead tr');
    const th = document.createElement('th');
    const columnIndex = headerRow.children.length;
    th.innerHTML = `
        <input type="text" value="新列${columnIndex + 1}" onchange="updateColumnHeader(this)" class="form-control form-control-sm">
        <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${columnIndex})" title="删除列">×</button>
    `;
    headerRow.appendChild(th);

    // 为每一行添加新列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    });
}

// 删除行
function deleteRow(button) {
    const row = button.closest('tr');
    row.remove();
}

// 删除列
function deleteColumn(button, columnIndex) {
    const table = button.closest('table');

    // 删除表头
    const headerRow = table.querySelector('thead tr');
    if (headerRow.children.length <= 1) {
        alert('至少需要保留一列');
        return;
    }
    headerRow.children[columnIndex].remove();

    // 删除每行对应的列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (row.children[columnIndex]) {
            row.children[columnIndex].remove();
        }
    });
}

// 清空表格
function clearTable(tableId) {
    if (confirm('确定要清空表格吗？')) {
        const table = document.getElementById(tableId);
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
    }
}

// 导出表格
function exportTable(tableId) {
    const table = document.getElementById(tableId);
    const data = getTableData(table);

    if (data.length === 0) {
        alert('表格为空，无法导出');
        return;
    }

    const csv = convertToCSV(data);
    downloadCSV(csv, `${tableId}_export.csv`);
}

// 获取表格数据
function getTableData(table) {
    const data = [];

    // 获取表头
    const headers = [];
    const headerInputs = table.querySelectorAll('thead input');
    headerInputs.forEach(input => {
        headers.push(input.value);
    });
    data.push(headers);

    // 获取数据行
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = [];
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            rowData.push(input.value);
        });
        if (rowData.some(cell => cell.trim() !== '')) {
            data.push(rowData);
        }
    });

    return data;
}

// 转换为CSV格式
function convertToCSV(data) {
    return data.map(row =>
        row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
}

// 下载CSV文件
function downloadCSV(csv, filename) {
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 处理CSV文件上传
function handleCSVFile(event, tabId = 'user') {
    const file = event.target.files[0];
    if (!file) {
        // 如果没有选择文件，禁用按钮
        const startBtn = document.getElementById(`${tabId}BatchStartBtn`) || document.getElementById('batchStartBtn');
        if (startBtn) {
            startBtn.disabled = true;
        }
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        csvData = parseCSV(csv);

        if (csvData.length > 0) {
            displayCSVPreview(csvData, tabId);
            // 按钮启用逻辑已在displayCSVPreview中处理
        } else {
            // 如果解析失败，禁用按钮
            const startBtn = document.getElementById(`${tabId}BatchStartBtn`) || document.getElementById('batchStartBtn');
            if (startBtn) {
                startBtn.disabled = true;
            }
        }
    };
    reader.readAsText(file, 'UTF-8');
}

// 解析CSV数据
function parseCSV(csv) {
    const lines = csv.split('\n');
    const result = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            const row = [];
            let current = '';
            let inQuotes = false;

            for (let j = 0; j < line.length; j++) {
                const char = line[j];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    row.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            row.push(current.trim());
            result.push(row);
        }
    }

    return result;
}

// 显示CSV预览
function displayCSVPreview(data, tabId) {
    const previewDiv = document.getElementById(`${tabId}CsvPreview`);
    const previewTable = document.getElementById(`${tabId}CsvPreviewTable`);
    const startBtn = document.getElementById(`${tabId}BatchStartBtn`);

    if (!previewDiv || !previewTable) return;

    previewTable.innerHTML = '';

    // 显示前10行数据
    const previewData = data.slice(0, 10);

    previewData.forEach((row, index) => {
        const tr = document.createElement('tr');
        if (index === 0) {
            // 表头
            row.forEach(cell => {
                const th = document.createElement('th');
                th.textContent = cell;
                tr.appendChild(th);
            });
        } else {
            // 数据行
            row.forEach(cell => {
                const td = document.createElement('td');
                td.textContent = cell;
                tr.appendChild(td);
            });
        }
        previewTable.appendChild(tr);
    });

    previewDiv.style.display = 'block';

    // 启用开始按钮
    if (startBtn) {
        startBtn.disabled = false;
        console.log(`CSV预览完成，启用按钮: ${tabId}BatchStartBtn`);
    }
}

// 添加粘贴事件监听
function addPasteEventListener(table) {
    table.addEventListener('paste', function(e) {
        e.preventDefault();

        // 获取粘贴的数据
        let pastedData = '';
        if (e.clipboardData) {
            pastedData = e.clipboardData.getData('text');
        } else if (window.clipboardData) {
            pastedData = window.clipboardData.getData('Text');
        }

        if (!pastedData.trim()) return;

        // 解析粘贴的数据（支持Excel复制的制表符分隔格式）
        const rows = pastedData.trim().split('\n');
        const parsedData = rows.map(row => {
            // 处理制表符分隔的数据（Excel格式）
            return row.split('\t').map(cell => cell.trim());
        });

        if (parsedData.length === 0) return;

        // 确定需要的列数
        const maxColumns = Math.max(...parsedData.map(row => row.length));
        const currentColumns = table.querySelector('thead tr').children.length;

        // 如果需要更多列，自动添加
        if (maxColumns > currentColumns) {
            for (let i = currentColumns; i < maxColumns; i++) {
                addTableColumnInternal(table, `列${i + 1}`);
            }
        }

        // 清空现有数据行
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        // 添加粘贴的数据
        parsedData.forEach(rowData => {
            const row = document.createElement('tr');

            for (let i = 0; i < maxColumns; i++) {
                const td = document.createElement('td');
                const value = rowData[i] || '';
                td.innerHTML = `
                    <input type="text" class="form-control form-control-sm" value="${escapeHtml(value)}" placeholder="输入数据">
                    <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
                `;
                row.appendChild(td);
            }

            tbody.appendChild(row);
        });

        // 如果没有数据行，至少添加一行空行
        if (parsedData.length === 0) {
            addTableRowToBody(tbody, maxColumns);
        }

        console.log(`成功粘贴 ${parsedData.length} 行 ${maxColumns} 列数据`);
    });
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 内部添加列函数（不触发事件）
function addTableColumnInternal(table, columnName) {
    // 添加表头
    const headerRow = table.querySelector('thead tr');
    const th = document.createElement('th');
    const columnIndex = headerRow.children.length;
    th.innerHTML = `
        <input type="text" value="${columnName}" onchange="updateColumnHeader(this)" class="form-control form-control-sm">
        <button type="button" class="delete-col-btn" onclick="deleteColumn(this, ${columnIndex})" title="删除列">×</button>
    `;
    headerRow.appendChild(th);

    // 为每一行添加新列
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const td = document.createElement('td');
        td.innerHTML = `
            <input type="text" class="form-control form-control-sm" placeholder="输入数据">
            <button type="button" class="delete-row-btn" onclick="deleteRow(this)" title="删除行">×</button>
        `;
        row.appendChild(td);
    });
}

// 更新列标题
function updateColumnHeader(input) {
    // 列标题更新逻辑已在input的onchange中处理
}

// 开始快速修复
function startQuickRepair(tabId = 'user') {
    if (isProcessing) {
        alert('正在处理中，请等待完成');
        return;
    }

    const tableId = tabId === 'user' ? 'quickRepairTable' : `${tabId}QuickRepairTable`;
    const table = document.getElementById(tableId);
    const data = getTableData(table);

    if (data.length <= 1) {
        alert('请先添加数据');
        return;
    }

    // 验证必填字段
    if (!validateTableData(data, tabId)) {
        return;
    }

    const threadsInput = document.getElementById(tabId === 'user' ? 'quickThreads' : `${tabId}QuickThreads`);
    const threads = parseInt(threadsInput.value) || 10;

    // 添加结果列
    addResultColumn(table);

    isProcessing = true;
    processTableData(data, tabId, threads);
}

// 验证表格数据
function validateTableData(data, tabId) {
    const config = tabConfigs[tabId];
    const headers = data[0];

    // 检查是否包含查询字段
    if (!headers.includes(config.queryField)) {
        alert(`表格必须包含查询字段: ${config.queryField}`);
        return false;
    }

    // 检查数据行是否为空
    for (let i = 1; i < data.length; i++) {
        const row = data[i];
        if (row.every(cell => !cell.trim())) {
            continue;
        }

        const queryFieldIndex = headers.indexOf(config.queryField);
        if (!row[queryFieldIndex] || !row[queryFieldIndex].trim()) {
            alert(`第${i}行的查询字段不能为空`);
            return false;
        }
    }

    return true;
}

// 添加结果列
function addResultColumn(table) {
    // 检查是否已经有结果列
    const headerRow = table.querySelector('thead tr');
    const lastHeader = headerRow.lastElementChild.querySelector('input');

    if (lastHeader.value !== 'result') {
        // 添加结果列表头
        const th = document.createElement('th');
        th.innerHTML = `
            <input type="text" value="result" class="form-control form-control-sm" readonly>
        `;
        headerRow.appendChild(th);

        // 为每一行添加结果列
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const td = document.createElement('td');
            td.innerHTML = '<span class="result-pending">等待中...</span>';
            row.appendChild(td);
        });
    }
}

// 处理表格数据
async function processTableData(data, tabId, threads) {
    const headers = data[0];
    const rows = data.slice(1).filter(row => row.some(cell => cell.trim()));
    const config = tabConfigs[tabId];

    // 分组处理
    const chunks = chunkArray(rows, threads);
    let processedCount = 0;

    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const promises = chunk.map((row, index) =>
            processRow(row, headers, config, processedCount + index, tabId)
        );

        await Promise.all(promises);
        processedCount += chunk.length;
    }

    isProcessing = false;
    alert('快速修复完成！');
}

// 处理单行数据
async function processRow(row, headers, config, rowIndex, tabId) {
    try {
        // 构建请求参数
        const params = {};
        headers.forEach((header, index) => {
            if (header !== 'result') {
                params[header] = row[index] || '';
            }
        });

        // 添加配置参数
        params.table_name = document.getElementById(`${tabId}TableName`).value;
        params.action = document.getElementById(`${tabId}Action`).value;
        params.query_field = document.getElementById(`${tabId}QueryField`).value;

        // 发送请求
        const response = await fetch(config.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(params)
        });

        const result = await response.json();

        // 更新结果
        updateRowResult(rowIndex, result.success ? '成功' : result.message || '失败', result.success, tabId);

    } catch (error) {
        updateRowResult(rowIndex, '网络错误', false, tabId);
    }
}

// 更新行结果
function updateRowResult(rowIndex, message, success, tabId) {
    const tableId = tabId === 'user' ? 'quickRepairTable' : `${tabId}QuickRepairTable`;
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    if (rows[rowIndex]) {
        const resultCell = rows[rowIndex].lastElementChild;
        const className = success ? 'result-success' : 'result-error';
        resultCell.innerHTML = `<span class="${className}">${message}</span>`;
    }
}

// 数组分块
function chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

// 开始大批量修复
function startBatchRepair(tabId = 'user') {
    if (isProcessing) {
        alert('正在处理中，请等待完成');
        return;
    }

    if (csvData.length <= 1) {
        alert('请先上传CSV文件');
        return;
    }

    // 验证CSV数据
    if (!validateTableData(csvData, tabId)) {
        return;
    }

    const threadsInput = document.getElementById(`${tabId}BatchThreads`);
    const threads = parseInt(threadsInput.value) || 50;

    isProcessing = true;
    processBatchData(csvData, tabId, threads);
}

// 处理批量数据
async function processBatchData(data, tabId, threads) {
    const headers = data[0];
    const rows = data.slice(1).filter(row => row.some(cell => cell.trim()));
    const config = tabConfigs[tabId];

    // 显示进度条
    showBatchProgress(tabId, rows.length);

    const startTime = Date.now();
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 分组处理
    const chunks = chunkArray(rows, threads);

    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const promises = chunk.map(row =>
            processBatchRow(row, headers, config)
        );

        const results = await Promise.all(promises);

        // 统计结果
        results.forEach(result => {
            if (result.success) {
                successCount++;
            } else {
                errorCount++;
            }
        });

        processedCount += chunk.length;

        // 更新进度
        updateBatchProgress(tabId, processedCount, rows.length, startTime);
    }

    // 完成处理
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;

    document.getElementById(`${tabId}TotalTime`).textContent = formatTime(totalTime);

    isProcessing = false;
    alert(`大批量修复完成！\n成功: ${successCount}\n失败: ${errorCount}\n总耗时: ${formatTime(totalTime)}`);
}

// 处理批量单行数据
async function processBatchRow(row, headers, config) {
    try {
        // 构建请求参数
        const params = {};
        headers.forEach((header, index) => {
            params[header] = row[index] || '';
        });

        // 添加配置参数
        params.table_name = config.tableName;
        params.action = config.action;
        params.query_field = config.queryField;

        // 发送请求
        const response = await fetch(config.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(params)
        });

        const result = await response.json();
        return result;

    } catch (error) {
        return { success: false, message: '网络错误' };
    }
}

// 显示批量进度
function showBatchProgress(tabId, totalCount) {
    const progressDiv = document.getElementById(`${tabId}BatchProgress`);
    const totalCountSpan = document.getElementById(`${tabId}TotalCount`);

    if (progressDiv && totalCountSpan) {
        progressDiv.style.display = 'block';
        totalCountSpan.textContent = totalCount;
    }
}

// 更新批量进度
function updateBatchProgress(tabId, processedCount, totalCount, startTime) {
    const progressBar = document.getElementById(`${tabId}ProgressBar`);
    const processedCountSpan = document.getElementById(`${tabId}ProcessedCount`);
    const elapsedTimeSpan = document.getElementById(`${tabId}ElapsedTime`);
    const remainingTimeSpan = document.getElementById(`${tabId}RemainingTime`);

    if (!progressBar) return;

    const percentage = Math.round((processedCount / totalCount) * 100);
    const elapsedTime = (Date.now() - startTime) / 1000;

    // 更新进度条
    progressBar.style.width = `${percentage}%`;
    progressBar.textContent = `${percentage}%`;

    // 更新计数
    if (processedCountSpan) {
        processedCountSpan.textContent = processedCount;
    }

    // 更新已用时间
    if (elapsedTimeSpan) {
        elapsedTimeSpan.textContent = `${elapsedTime.toFixed(2)}秒`;
    }

    // 计算预计剩余时间
    if (remainingTimeSpan && processedCount > 0) {
        const avgTimePerItem = elapsedTime / processedCount;
        const remainingItems = totalCount - processedCount;
        const remainingTime = avgTimePerItem * remainingItems;

        remainingTimeSpan.textContent = formatTime(remainingTime);
    }
}

// 格式化时间
function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(2)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${hours}时${minutes}分${remainingSeconds}秒`;
    }
}

// 兼容性函数 - 为了向后兼容原始HTML中的函数调用
window.handleCSVFileCompat = function(event) {
    handleCSVFile(event, currentTab);
}

window.startQuickRepairCompat = function() {
    startQuickRepair(currentTab);
}

window.startBatchRepairCompat = function() {
    startBatchRepair(currentTab);
}
