<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Node操作功能测试 - 数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-cogs text-primary"></i>
                    Node操作功能测试页面
                </h1>
                <div class="text-center mb-4">
                    <a href="index.html" class="btn btn-primary me-2">
                        <i class="fas fa-home"></i> 返回主页
                    </a>
                    <a href="test-styles.html" class="btn btn-info me-2">
                        <i class="fas fa-palette"></i> 样式测试
                    </a>
                    <a href="test-enhanced.html" class="btn btn-success">
                        <i class="fas fa-rocket"></i> 功能测试
                    </a>
                </div>
            </div>
        </div>

        <!-- 新功能展示 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star"></i> Node操作新功能展示</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-1 text-primary"></i> 动态配置表单</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <form>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label">表名</label>
                                                    <select class="form-select">
                                                        <option value="">请选择表名</option>
                                                        <option value="table1">table1</option>
                                                        <option value="table2">table2</option>
                                                        <option value="table3">table3</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">动作</label>
                                                    <select class="form-select" onchange="showDynamicFields(this.value)">
                                                        <option value="新增">新增</option>
                                                        <option value="更新">更新</option>
                                                        <option value="删除">删除</option>
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <!-- 更新操作的额外字段 -->
                                            <div class="row mt-3" id="updateFieldsDemo" style="display: none;">
                                                <div class="col-md-6">
                                                    <label class="form-label">查询条件</label>
                                                    <input type="text" class="form-control" placeholder="可填写多个字段，并用逗号隔开">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">更新字段</label>
                                                    <input type="text" class="form-control" placeholder="可填写多个字段，并用逗号隔开">
                                                </div>
                                            </div>
                                            
                                            <!-- 删除操作的额外字段 -->
                                            <div class="row mt-3" id="deleteFieldsDemo" style="display: none;">
                                                <div class="col-md-12">
                                                    <label class="form-label">条件删除</label>
                                                    <input type="text" class="form-control" placeholder="根据查询字段条件删除记录行">
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    根据选择的动作自动显示对应的配置字段
                                </small>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-2 text-success"></i> Excel风格表格</h6>
                                <div class="table-container-wrapper">
                                    <div class="table-container">
                                        <table class="table excel-table">
                                            <thead>
                                                <tr>
                                                    <th>channel</th>
                                                    <th>sellerId</th>
                                                    <th>skuId</th>
                                                    <th>mskuId</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="editable" contenteditable="true">amazon</td>
                                                    <td class="editable" contenteditable="true">seller001</td>
                                                    <td class="editable" contenteditable="true">SKU123</td>
                                                    <td class="editable" contenteditable="true" style="position: relative;">
                                                        MSKU456
                                                        <button class="delete-row-btn">×</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="editable" contenteditable="true">ebay</td>
                                                    <td class="editable" contenteditable="true">seller002</td>
                                                    <td class="editable" contenteditable="true">SKU789</td>
                                                    <td class="editable" contenteditable="true" style="position: relative;">
                                                        MSKU012
                                                        <button class="delete-row-btn">×</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="add-column-container">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus"></i> 新增列
                                        </button>
                                    </div>
                                </div>
                                <div class="table-actions mt-2">
                                    <button class="btn btn-outline-primary btn-sm me-2">
                                        <i class="fas fa-plus"></i> 新增行
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i> 清空表格
                                    </button>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle"></i>
                                    Excel风格的表格，悬停显示删除按钮，支持Tab键导航
                                </small>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6><i class="fas fa-3 text-warning"></i> 修复原因对话框</h6>
                                <button class="btn btn-primary" onclick="showDemoDialog()">
                                    <i class="fas fa-play"></i> 模拟开始修复
                                </button>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        首次点击会弹出修复原因对话框，后续点击直接执行
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-4 text-info"></i> 多接口支持</h6>
                                <div class="alert alert-info">
                                    <h6>接口配置示例：</h6>
                                    <pre class="mb-0"><code>{
  "table1": {
    "create": "api/node/table1/create.php",
    "read": "api/node/table1/read.php", 
    "update": "api/node/table1/update.php",
    "delete": "api/node/table1/delete.php"
  }
}</code></pre>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    根据表名和动作自动选择对应的接口
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能对比 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-balance-scale"></i> 功能对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>原有功能</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-muted me-2"></i>
                                        固定的用户、订单等15个标签页
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-muted me-2"></i>
                                        简单的表单输入样式
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-muted me-2"></i>
                                        单一的查询字段配置
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-muted me-2"></i>
                                        固定的接口调用方式
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Node操作新功能</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        专门的node操作标签页
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        Excel风格的表格界面
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        动态的配置字段（查询条件、更新字段、条件删除）
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        多接口支持，根据表名和动作自动选择
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        修复原因对话框，全局保存
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        支持POST、PUT、DELETE不同请求方式
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-book"></i> 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>1. 配置操作</h6>
                                <ol>
                                    <li>选择表名</li>
                                    <li>选择动作（新增/更新/删除）</li>
                                    <li>根据动作填写额外字段</li>
                                </ol>
                            </div>
                            <div class="col-md-4">
                                <h6>2. 数据输入</h6>
                                <ol>
                                    <li>在Excel风格表格中输入数据</li>
                                    <li>支持Tab键在单元格间导航</li>
                                    <li>悬停显示删除按钮</li>
                                </ol>
                            </div>
                            <div class="col-md-4">
                                <h6>3. 执行修复</h6>
                                <ol>
                                    <li>点击开始修复按钮</li>
                                    <li>首次填写修复原因</li>
                                    <li>系统自动选择对应接口执行</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模拟对话框 -->
    <div class="modal fade" id="demoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>填写修复原因
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">修复原因</label>
                        <input type="text" class="form-control" placeholder="请填写修复原因，格式如"修复上架编号_linweihong_0719"">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        修复原因将在本次会话中保存，切换标签页不会丢失。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确认开始修复</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示动态字段
        function showDynamicFields(action) {
            const updateFields = document.getElementById('updateFieldsDemo');
            const deleteFields = document.getElementById('deleteFieldsDemo');
            
            updateFields.style.display = 'none';
            deleteFields.style.display = 'none';
            
            if (action === '更新') {
                updateFields.style.display = 'block';
            } else if (action === '删除') {
                deleteFields.style.display = 'block';
            }
        }
        
        // 显示演示对话框
        function showDemoDialog() {
            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }
    </script>
</body>
</html>
