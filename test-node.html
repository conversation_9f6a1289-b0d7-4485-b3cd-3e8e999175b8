<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Node操作功能测试 - 数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-cogs text-primary"></i>
                    Node操作功能测试页面
                </h1>
                <div class="text-center mb-4">
                    <a href="index.html" class="btn btn-primary me-2">
                        <i class="fas fa-home"></i> 返回主页
                    </a>
                    <a href="test-styles.html" class="btn btn-info">
                        <i class="fas fa-palette"></i> 样式测试
                    </a>
                </div>
            </div>
        </div>

        <!-- Node操作功能展示 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Node操作功能展示</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-1 text-primary"></i> 配置表单</h6>
                                <form>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">表名</label>
                                            <select class="form-select" onchange="showTableSelection()">
                                                <option value="">请选择表名</option>
                                                <option value="table1">table1</option>
                                                <option value="table2">table2</option>
                                                <option value="table3">table3</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">动作</label>
                                            <select class="form-select" onchange="showActionFields(this.value)">
                                                <option value="新增">新增</option>
                                                <option value="更新">更新</option>
                                                <option value="删除">删除</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 更新操作的额外字段 -->
                                    <div class="row mt-3" id="updateFieldsDemo" style="display: none;">
                                        <div class="col-md-6">
                                            <label class="form-label">查询条件</label>
                                            <input type="text" class="form-control" placeholder="可填写多个字段，并用逗号隔开">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">更新字段</label>
                                            <input type="text" class="form-control" placeholder="可填写多个字段，并用逗号隔开">
                                        </div>
                                    </div>
                                    
                                    <!-- 删除操作的额外字段 -->
                                    <div class="row mt-3" id="deleteFieldsDemo" style="display: none;">
                                        <div class="col-md-12">
                                            <label class="form-label">条件删除</label>
                                            <input type="text" class="form-control" placeholder="根据查询字段条件删除记录行">
                                        </div>
                                    </div>
                                </form>
                                
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>说明：</strong>切换标签页会清空字段选择，动作选择会显示对应的额外字段
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-2 text-success"></i> Excel风格表格</h6>
                                <div class="table-container">
                                    <table class="excel-table">
                                        <thead>
                                            <tr>
                                                <th>channel</th>
                                                <th>sellerId</th>
                                                <th>skuId</th>
                                                <th style="position: relative;">
                                                    mskuId
                                                    <button class="add-column-btn" title="新增列">+</button>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="editable" contenteditable="true">amazon</td>
                                                <td class="editable" contenteditable="true">seller001</td>
                                                <td class="editable" contenteditable="true">SKU001</td>
                                                <td class="editable" contenteditable="true" style="position: relative;">
                                                    MSKU001
                                                    <button class="delete-row-btn" title="删除行">×</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="editable" contenteditable="true">ebay</td>
                                                <td class="editable" contenteditable="true">seller002</td>
                                                <td class="editable" contenteditable="true">SKU002</td>
                                                <td class="editable" contenteditable="true" style="position: relative;">
                                                    MSKU002
                                                    <button class="delete-row-btn" title="删除行">×</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="d-flex justify-content-between mt-2">
                                    <button class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus"></i> 新增行
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus"></i> 新增列
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="alert alert-success">
                                        <i class="fas fa-check me-2"></i>
                                        <strong>特点：</strong>纯表格样式，类似Excel，悬停显示删除按钮
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6><i class="fas fa-3 text-warning"></i> 修复原因对话框</h6>
                                <button class="btn btn-primary" onclick="showDemoDialog()">
                                    <i class="fas fa-play"></i> 模拟开始修复
                                </button>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        点击按钮体验修复原因对话框，原因会在会话中保持
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-4 text-info"></i> 多接口支持</h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-code me-2"></i>
                                    <strong>接口配置示例：</strong>
                                    <pre class="mt-2 mb-0" style="font-size: 12px;">
{
  "table1": {
    "create": "api/node/table1/create.php",
    "read": "api/node/table1/read.php", 
    "update": "api/node/table1/update.php",
    "delete": "api/node/table1/delete.php"
  }
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作流程说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-flow-chart"></i> 操作流程说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>新增操作</h6>
                                <ol>
                                    <li>选择表名</li>
                                    <li>选择"新增"动作</li>
                                    <li>填写表格数据</li>
                                    <li>点击开始修复</li>
                                    <li>发送POST请求到create接口</li>
                                </ol>
                            </div>
                            <div class="col-md-4">
                                <h6>更新操作</h6>
                                <ol>
                                    <li>选择表名</li>
                                    <li>选择"更新"动作</li>
                                    <li>填写查询条件和更新字段</li>
                                    <li>填写表格数据</li>
                                    <li>先GET查询，再PUT更新</li>
                                </ol>
                            </div>
                            <div class="col-md-4">
                                <h6>删除操作</h6>
                                <ol>
                                    <li>选择表名</li>
                                    <li>选择"删除"动作</li>
                                    <li>填写条件删除字段</li>
                                    <li>填写表格数据</li>
                                    <li>发送DELETE请求到delete接口</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术特性 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> 技术特性</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>已实现功能</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Node操作标签页独立配置
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        动态字段显示/隐藏
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Excel风格纯表格
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        修复原因对话框
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        多接口扩展支持
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>扩展点</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-code text-info me-2"></i>
                                        tabConfigs.node.apiEndpoints - 接口配置
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-code text-info me-2"></i>
                                        tabConfigs.node.tableOptions - 表名选项
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-code text-info me-2"></i>
                                        processNodeOperation - 操作逻辑
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-code text-info me-2"></i>
                                        makeRequest - 请求处理
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toast 消息将动态添加到这里 -->
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 显示动作字段
        function showActionFields(action) {
            const updateFields = document.getElementById('updateFieldsDemo');
            const deleteFields = document.getElementById('deleteFieldsDemo');
            
            updateFields.style.display = 'none';
            deleteFields.style.display = 'none';
            
            if (action === '更新') {
                updateFields.style.display = 'block';
            } else if (action === '删除') {
                deleteFields.style.display = 'block';
            }
        }

        // 显示表选择
        function showTableSelection() {
            showToast('表名已选择，接口配置将自动匹配', 'info');
        }

        // 显示演示对话框
        function showDemoDialog() {
            const modalHtml = `
                <div class="modal fade" id="demoModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-edit me-2"></i>填写修复原因
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">修复原因</label>
                                    <textarea class="form-control" rows="3" 
                                        placeholder="请填写修复原因，格式如"修复上架编号_linweihong_0719""></textarea>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    修复原因将在本次会话中保持，切换标签页不需要重新填写
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="confirmDemo()">确认开始修复</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // 确认演示
        function confirmDemo() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('demoModal'));
            modal.hide();
            showToast('修复原因已保存，开始执行修复操作', 'success');
        }
    </script>
</body>
</html>
