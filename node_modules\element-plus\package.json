{"_from": "element-plus@^2.4.0", "_id": "element-plus@2.10.4", "_inBundle": false, "_integrity": "sha512-UD4elWHrCnp1xlPhbXmVcaKFLCRaRAY6WWRwemGfGW3ceIjXm9fSYc9RNH3AiOEA6Ds1p9ZvhCs76CR9J8Vd+A==", "_location": "/element-plus", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "element-plus@^2.4.0", "name": "element-plus", "escapedName": "element-plus", "rawSpec": "^2.4.0", "saveSpec": null, "fetchSpec": "^2.4.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/element-plus/-/element-plus-2.10.4.tgz", "_shasum": "72de60a6074be79f9f1b299f422e7ac96a3b5e9a", "_spec": "element-plus@^2.4.0", "_where": "/mnt/e/www/demo1", "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "bugs": {"url": "https://github.com/element-plus/element-plus/issues"}, "bundleDependencies": false, "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "deprecated": false, "description": "A Component Library for Vue 3", "devDependencies": {"@types/node": "*", "csstype": "^2.6.20", "vue": "^3.2.37", "vue-router": "^4.0.16"}, "exports": {".": {"types": "./es/index.d.ts", "import": "./es/index.mjs", "require": "./lib/index.js"}, "./global": {"types": "./global.d.ts"}, "./es": {"types": "./es/index.d.ts", "import": "./es/index.mjs"}, "./lib": {"types": "./lib/index.d.ts", "require": "./lib/index.js"}, "./es/*.mjs": {"types": "./es/*.d.ts", "import": "./es/*.mjs"}, "./es/*": {"types": ["./es/*.d.ts", "./es/*/index.d.ts"], "import": "./es/*.mjs"}, "./lib/*.js": {"types": "./lib/*.d.ts", "require": "./lib/*.js"}, "./lib/*": {"types": ["./lib/*.d.ts", "./lib/*/index.d.ts"], "require": "./lib/*.js"}, "./*": "./*"}, "gitHead": "3d76b2c6c76256cf38777b15ae77f3f979538fde", "homepage": "https://element-plus.org/", "jsdelivr": "dist/index.full.js", "keywords": ["element-plus", "element", "component library", "ui framework", "ui", "vue"], "license": "MIT", "main": "lib/index.js", "module": "es/index.mjs", "name": "element-plus", "peerDependencies": {"vue": "^3.2.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/element-plus/element-plus.git"}, "sideEffects": ["dist/*", "theme-chalk/**/*.css", "theme-chalk/src/**/*.scss", "es/components/*/style/*", "lib/components/*/style/*"], "style": "dist/index.css", "types": "es/index.d.ts", "unpkg": "dist/index.full.js", "version": "2.10.4", "vetur": {"tags": "tags.json", "attributes": "attributes.json"}, "web-types": "web-types.json"}