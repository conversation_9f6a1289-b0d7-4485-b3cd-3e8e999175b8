{"_from": "to-regex-range@^5.0.1", "_id": "to-regex-range@5.0.1", "_inBundle": false, "_integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "_location": "/to-regex-range", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "to-regex-range@^5.0.1", "name": "to-regex-range", "escapedName": "to-regex-range", "rawSpec": "^5.0.1", "saveSpec": null, "fetchSpec": "^5.0.1"}, "_requiredBy": ["/fill-range"], "_resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "_shasum": "1648c44aae7c8d988a326018ed72f5b4dd0392e4", "_spec": "to-regex-range@^5.0.1", "_where": "/mnt/e/www/demo1/node_modules/fill-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "dependencies": {"is-number": "^7.0.0"}, "deprecated": false, "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "engines": {"node": ">=8.0"}, "files": ["index.js"], "homepage": "https://github.com/micromatch/to-regex-range", "keywords": ["bash", "date", "expand", "expansion", "expression", "glob", "match", "match date", "match number", "match numbers", "match year", "matches", "matching", "number", "numbers", "numerical", "range", "ranges", "regex", "regexp", "regular", "regular expression", "sequence"], "license": "MIT", "main": "index.js", "name": "to-regex-range", "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "scripts": {"test": "mocha"}, "verb": {"layout": "default", "toc": false, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": {"examples": {"displayName": "examples"}}, "related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}}, "version": "5.0.1"}