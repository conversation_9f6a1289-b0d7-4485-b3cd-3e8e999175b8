{"_from": "ufo@^1.5.4", "_id": "ufo@1.6.1", "_inBundle": false, "_integrity": "sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==", "_location": "/ufo", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ufo@^1.5.4", "name": "ufo", "escapedName": "ufo", "rawSpec": "^1.5.4", "saveSpec": null, "fetchSpec": "^1.5.4"}, "_requiredBy": ["/mlly"], "_resolved": "https://registry.npmjs.org/ufo/-/ufo-1.6.1.tgz", "_shasum": "ac2db1d54614d1b22c1d603e3aef44a85d8f146b", "_spec": "ufo@^1.5.4", "_where": "/mnt/e/www/demo1/node_modules/mlly", "bugs": {"url": "https://github.com/unjs/ufo/issues"}, "bundleDependencies": false, "deprecated": false, "description": "URL utils for humans", "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.24.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "untyped": "^2.0.0", "vitest": "^3.1.1"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/unjs/ufo#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "ufo", "packageManager": "pnpm@10.7.1", "repository": {"type": "git", "url": "git+https://github.com/unjs/ufo.git"}, "scripts": {"automd": "automd", "build": "automd && unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --typecheck"}, "sideEffects": false, "types": "./dist/index.d.ts", "version": "1.6.1"}