<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入通用数据库类
require_once 'user_repair.php';

// 商品数据修复类
class ProductRepair {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function processRequest($data) {
        try {
            // 验证必要参数
            if (!isset($data['table_name']) || !isset($data['action']) || !isset($data['query_field'])) {
                throw new Exception("缺少必要参数");
            }
            
            $tableName = $this->sanitizeTableName($data['table_name']);
            $action = $data['action'];
            $queryField = $data['query_field'];
            
            // 商品特殊验证
            $this->validateProductData($data, $action);
            
            // 根据动作执行不同操作
            switch ($action) {
                case 'update':
                    return $this->updateProduct($tableName, $queryField, $data);
                case 'insert':
                    return $this->insertProduct($tableName, $data);
                case 'delete':
                    return $this->deleteProduct($tableName, $queryField, $data);
                default:
                    throw new Exception("不支持的操作类型: " . $action);
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function validateProductData($data, $action) {
        // 价格验证
        if (isset($data['price']) && (!is_numeric($data['price']) || $data['price'] < 0)) {
            throw new Exception("商品价格必须是非负数字");
        }
        
        // 库存验证
        if (isset($data['stock']) && (!is_numeric($data['stock']) || $data['stock'] < 0)) {
            throw new Exception("商品库存必须是非负整数");
        }
        
        // 商品名称验证
        if (isset($data['name']) && (empty(trim($data['name'])) || strlen($data['name']) > 255)) {
            throw new Exception("商品名称不能为空且长度不能超过255字符");
        }
        
        // SKU验证
        if (isset($data['sku']) && !empty($data['sku'])) {
            if (!preg_match('/^[A-Z0-9\-_]+$/i', $data['sku'])) {
                throw new Exception("SKU只能包含字母、数字、连字符和下划线");
            }
        }
    }
    
    private function updateProduct($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        // 先检查商品是否存在
        $checkSql = "SELECT COUNT(*) FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute(['query_value' => $data[$queryField]]);
        
        if ($checkStmt->fetchColumn() == 0) {
            throw new Exception("商品不存在");
        }
        
        // 如果更新SKU，检查是否重复
        if (isset($data['sku']) && !empty($data['sku'])) {
            $skuCheckSql = "SELECT COUNT(*) FROM `{$tableName}` WHERE `sku` = :sku AND `{$queryField}` != :query_value";
            $skuCheckStmt = $conn->prepare($skuCheckSql);
            $skuCheckStmt->execute(['sku' => $data['sku'], 'query_value' => $data[$queryField]]);
            
            if ($skuCheckStmt->fetchColumn() > 0) {
                throw new Exception("SKU已存在");
            }
        }
        
        // 构建更新字段
        $updateFields = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field']) && $key !== $queryField) {
                $updateFields[] = "`{$key}` = :{$key}";
                $params[$key] = $value;
            }
        }
        
        if (empty($updateFields)) {
            throw new Exception("没有要更新的字段");
        }
        
        // 添加更新时间
        $updateFields[] = "`updated_at` = NOW()";
        
        $sql = "UPDATE `{$tableName}` SET " . implode(', ', $updateFields) . " WHERE `{$queryField}` = :query_value";
        $params['query_value'] = $data[$queryField];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        if ($affectedRows > 0) {
            return [
                'success' => true,
                'message' => "成功更新商品",
                'affected_rows' => $affectedRows
            ];
        } else {
            return [
                'success' => false,
                'message' => "商品数据未发生变化"
            ];
        }
    }
    
    private function insertProduct($tableName, $data) {
        $conn = $this->db->getConnection();
        
        // 检查必要字段
        $requiredFields = ['name', 'price'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("缺少必要字段: {$field}");
            }
        }
        
        // 检查SKU是否重复
        if (isset($data['sku']) && !empty($data['sku'])) {
            $skuCheckSql = "SELECT COUNT(*) FROM `{$tableName}` WHERE `sku` = :sku";
            $skuCheckStmt = $conn->prepare($skuCheckSql);
            $skuCheckStmt->execute(['sku' => $data['sku']]);
            
            if ($skuCheckStmt->fetchColumn() > 0) {
                throw new Exception("SKU已存在");
            }
        }
        
        // 构建插入字段
        $fields = [];
        $placeholders = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field'])) {
                $fields[] = "`{$key}`";
                $placeholders[] = ":{$key}";
                $params[$key] = $value;
            }
        }
        
        // 添加创建时间
        $fields[] = "`created_at`";
        $placeholders[] = "NOW()";
        
        $sql = "INSERT INTO `{$tableName}` (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        return [
            'success' => true,
            'message' => "成功创建商品",
            'insert_id' => $conn->lastInsertId()
        ];
    }
    
    private function deleteProduct($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        if (!isset($data[$queryField])) {
            throw new Exception("缺少查询字段值");
        }
        
        // 检查商品是否存在
        $checkSql = "SELECT COUNT(*) FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute(['query_value' => $data[$queryField]]);
        
        if ($checkStmt->fetchColumn() == 0) {
            throw new Exception("商品不存在");
        }
        
        // 可以添加额外的业务逻辑检查，比如检查是否有关联订单等
        
        $sql = "DELETE FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $params = ['query_value' => $data[$queryField]];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        return [
            'success' => true,
            'message' => "成功删除商品",
            'affected_rows' => $affectedRows
        ];
    }
    
    private function sanitizeTableName($tableName) {
        // 只允许字母、数字和下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        return $tableName;
    }
}

// 主处理逻辑
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("只支持POST请求");
    }
    
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("无效的JSON数据");
    }
    
    $productRepair = new ProductRepair();
    $result = $productRepair->processRequest($data);
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
