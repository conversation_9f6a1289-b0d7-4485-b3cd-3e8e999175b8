{"_from": "vue-demi@*", "_id": "vue-demi@0.14.10", "_inBundle": false, "_integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==", "_location": "/vue-demi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-demi@*", "name": "vue-demi", "escapedName": "vue-demi", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@vueuse/core", "/@vueuse/shared", "/pinia"], "_resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz", "_shasum": "afc78de3d6f9e11bf78c55e8510ee12814522f04", "_spec": "vue-demi@*", "_where": "/mnt/e/www/demo1/node_modules/@vueuse/core", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "bundleDependencies": false, "deprecated": false, "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs"}, "./*": "./*"}, "files": ["lib", "bin", "scripts"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vue-demi#readme", "jsdelivr": "lib/index.iife.js", "license": "MIT", "main": "lib/index.cjs", "module": "lib/index.mjs", "name": "vue-demi", "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "scripts": {"postinstall": "node -e \"try{require('./scripts/postinstall.js')}catch(e){}\"", "release": "npx bumpp --tag --commit --push && npm publish"}, "types": "lib/index.d.ts", "unpkg": "lib/index.iife.js", "version": "0.14.10"}