{"_from": "memoize-one@^6.0.0", "_id": "memoize-one@6.0.0", "_inBundle": false, "_integrity": "sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==", "_location": "/memoize-one", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "memoize-one@^6.0.0", "name": "memoize-one", "escapedName": "memoize-one", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz", "_shasum": "b2591b871ed82948aee4727dc6abceeeac8c1045", "_spec": "memoize-one@^6.0.0", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/alexreardon/memoize-one/issues"}, "bundleDependencies": false, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "dependencies": {}, "deprecated": false, "description": "A memoization library which only remembers the latest invocation", "devDependencies": {"@size-limit/preset-small-lib": "^5.0.4", "@types/benchmark": "^2.1.1", "@types/jest": "^27.0.2", "@types/lodash.isequal": "^4.5.5", "@types/lodash.memoize": "^4.1.6", "@types/node": "^16.10.1", "@typescript-eslint/eslint-plugin": "^4.31.2", "@typescript-eslint/parser": "^4.31.2", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.2", "eslint-plugin-prettier": "^4.0.0", "expect-type": "^0.12.0", "fast-memoize": "^2.5.2", "jest": "^27.2.2", "lodash.isequal": "^4.5.0", "lodash.memoize": "^4.1.2", "markdown-table": "^3.0.1", "mem": "^9.0.1", "memoizee": "^0.4.15", "moize": "^6.1.0", "nanocolors": "^0.2.9", "ora": "^6.0.1", "prettier": "2.4.1", "rimraf": "3.0.2", "rollup": "^2.57.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^5.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "tslib": "^2.3.1", "typescript": "^4.4.3"}, "files": ["/dist", "/src"], "homepage": "https://github.com/alexreardon/memoize-one#readme", "keywords": ["memoize", "memoization", "cache", "performance"], "license": "MIT", "main": "dist/memoize-one.cjs.js", "module": "dist/memoize-one.esm.js", "name": "memoize-one", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/memoize-one.git"}, "scripts": {"build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "yarn rimraf dist", "build:dist": "yarn rollup -c", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "build:typescript": "yarn tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "eslint:check": "eslint $npm_package_config_prettier_target", "perf": "yarn ts-node ./benchmarks/shallow-equal.ts", "perf:library-comparison": "yarn build && node ./benchmarks/library-comparison.js", "prepublishOnly": "yarn build", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "test": "yarn jest", "test:size": "yarn build && yarn size-limit", "typescript:check": "yarn tsc --noEmit", "validate": "yarn prettier:check && yarn eslint:check && yarn typescript:check"}, "sideEffects": false, "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "234B"}, {"path": "dist/memoize-one.js", "limit": "234B"}, {"path": "dist/memoize-one.cjs.js", "limit": "230B"}, {"path": "dist/memoize-one.esm.js", "limit": "246B"}], "types": "dist/memoize-one.d.ts", "version": "6.0.0"}