{"_from": "strip-literal@^2.1.1", "_id": "strip-literal@2.1.1", "_inBundle": false, "_integrity": "sha512-631UJ6O00eNGfMiWG78ck80dfBab8X6IVFB51jZK5Icd7XAs60Z5y7QdSd/wGIklnWvRbUNloVzhOKKmutxQ6Q==", "_location": "/strip-literal", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-literal@^2.1.1", "name": "strip-literal", "escapedName": "strip-literal", "rawSpec": "^2.1.1", "saveSpec": null, "fetchSpec": "^2.1.1"}, "_requiredBy": ["/unimport"], "_resolved": "https://registry.npmjs.org/strip-literal/-/strip-literal-2.1.1.tgz", "_shasum": "26906e65f606d49f748454a08084e94190c2e5ad", "_spec": "strip-literal@^2.1.1", "_where": "/mnt/e/www/demo1/node_modules/unimport", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/strip-literal/issues"}, "bundleDependencies": false, "dependencies": {"js-tokens": "^9.0.1"}, "deprecated": false, "description": "Strip comments and string literals from JavaScript code", "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@antfu/ni": "^0.23.1", "@types/node": "^22.9.3", "bumpp": "^9.8.1", "eslint": "^9.15.0", "esmo": "^4.8.0", "pnpm": "^9.14.2", "rimraf": "^6.0.1", "three": "^0.170.0", "typescript": "^5.7.2", "unbuild": "^2.0.0", "vite": "^5.4.11", "vitest": "^2.1.5", "vue": "^3.5.13"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/strip-literal#readme", "keywords": [], "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "strip-literal", "packageManager": "pnpm@9.14.2", "repository": {"type": "git", "url": "git+https://github.com/antfu/strip-literal.git"}, "scripts": {"bench": "vitest bench", "build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp --commit --push --tag && npm publish", "start": "esmo src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}, "sideEffects": false, "types": "./dist/index.d.ts", "version": "2.1.1"}