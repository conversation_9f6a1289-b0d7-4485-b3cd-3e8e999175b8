{"_from": "js-tokens@^9.0.1", "_id": "js-tokens@9.0.1", "_inBundle": false, "_integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "_location": "/js-tokens", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "js-tokens@^9.0.1", "name": "js-tokens", "escapedName": "js-tokens", "rawSpec": "^9.0.1", "saveSpec": null, "fetchSpec": "^9.0.1"}, "_requiredBy": ["/strip-literal"], "_resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "_shasum": "2ec43964658435296f6761b34e10671c2d9527f4", "_spec": "js-tokens@^9.0.1", "_where": "/mnt/e/www/demo1/node_modules/strip-literal", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny JavaScript tokenizer.", "exports": "./index.js", "homepage": "https://github.com/lydell/js-tokens#readme", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "license": "MIT", "name": "js-tokens", "repository": {"type": "git", "url": "git+https://github.com/lydell/js-tokens.git"}, "type": "commonjs", "version": "9.0.1"}