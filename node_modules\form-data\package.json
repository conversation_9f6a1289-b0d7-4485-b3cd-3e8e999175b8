{"_from": "form-data@^4.0.0", "_id": "form-data@4.0.4", "_inBundle": false, "_integrity": "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==", "_location": "/form-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "form-data@^4.0.0", "name": "form-data", "escapedName": "form-data", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz", "_shasum": "784cdcce0669a9d68e94d11ac4eea98088edd2c4", "_spec": "form-data@^4.0.0", "_where": "/mnt/e/www/demo1/node_modules/axios", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": "./lib/browser", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "bundleDependencies": false, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "deprecated": false, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"@ljharb/eslint-config": "^21.2.0", "auto-changelog": "^2.5.0", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "=8.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "pre-commit": "^1.2.2", "predict-v8-randomness": "^1.0.35", "puppeteer": "^1.20.0", "request": "~2.87.0", "rimraf": "^2.7.1", "semver": "^6.3.1", "tape": "^5.9.0"}, "engines": {"node": ">= 6"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "lint": "eslint --ext=js,mjs .", "postpack": "npm run restore-readme", "posttest": "npx npm@'>=10.2' audit --production", "posttests-only": "istanbul report lcov text", "postupdate-readme": "mv README.md.bak READ.ME.md.bak", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "predebug": "rimraf coverage test/tmp", "prepack": "npm run update-readme", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "report": "istanbul report lcov text", "restore-readme": "mv READ.ME.md.bak README.md", "test": "npm run tests-only", "tests-only": "istanbul cover test/run.js", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md", "version": "auto-changelog && git add CHANGELOG.md"}, "typings": "./index.d.ts", "version": "4.0.4"}