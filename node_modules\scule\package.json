{"_from": "scule@^1.3.0", "_id": "scule@1.3.0", "_inBundle": false, "_integrity": "sha512-6FtHJEvt+pVMIB9IBY+IcCJ6Z5f1iQnytgyfKMhDKgmzYG+TeH/wx1y3l27rshSbLiSanrR9ffZDrEsmjlQF2g==", "_location": "/scule", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "scule@^1.3.0", "name": "scule", "escapedName": "scule", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/unimport"], "_resolved": "https://registry.npmjs.org/scule/-/scule-1.3.0.tgz", "_shasum": "6efbd22fd0bb801bdcc585c89266a7d2daa8fbd3", "_spec": "scule@^1.3.0", "_where": "/mnt/e/www/demo1/node_modules/unimport", "bugs": {"url": "https://github.com/unjs/scule/issues"}, "bundleDependencies": false, "deprecated": false, "description": "String case utils", "devDependencies": {"@types/node": "^20.11.3", "@vitest/coverage-v8": "^1.2.0", "changelogen": "^0.5.5", "eslint": "^8.56.0", "eslint-config-unjs": "^0.2.1", "prettier": "^3.2.2", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vitest": "^1.2.0"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/unjs/scule#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "scule", "packageManager": "pnpm@8.14.1", "repository": {"type": "git", "url": "git+https://github.com/unjs/scule.git"}, "scripts": {"build": "unbuild", "dev": "vitest dev --typecheck", "lint": "eslint --cache --ext .ts,.js,.mjs,.cjs . && prettier -c src test", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --typecheck --coverage"}, "sideEffects": false, "types": "./dist/index.d.ts", "version": "1.3.0"}