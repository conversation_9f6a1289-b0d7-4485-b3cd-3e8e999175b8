{"_from": "@vueuse/shared@9.13.0", "_id": "@vueuse/shared@9.13.0", "_inBundle": false, "_integrity": "sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==", "_location": "/@vueuse/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vueuse/shared@9.13.0", "name": "@vueuse/shared", "escapedName": "@vueuse%2fshared", "scope": "@vueuse", "rawSpec": "9.13.0", "saveSpec": null, "fetchSpec": "9.13.0"}, "_requiredBy": ["/@vueuse/core"], "_resolved": "https://registry.npmjs.org/@vueuse/shared/-/shared-9.13.0.tgz", "_shasum": "089ff4cc4e2e7a4015e57a8f32e4b39d096353b9", "_spec": "@vueuse/shared@9.13.0", "_where": "/mnt/e/www/demo1/node_modules/@vueuse/core", "author": {"name": "<PERSON>", "email": "https://github.com/antfu"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "bundleDependencies": false, "dependencies": {"vue-demi": "*"}, "deprecated": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*"}, "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/shared#readme", "jsdelivr": "./index.iife.min.js", "keywords": ["vue", "vue-use", "utils"], "license": "MIT", "main": "./index.cjs", "module": "./index.mjs", "name": "@vueuse/shared", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/shared"}, "sideEffects": false, "types": "./index.d.ts", "unpkg": "./index.iife.min.js", "version": "9.13.0"}