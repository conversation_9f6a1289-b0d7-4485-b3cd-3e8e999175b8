{"_from": "reusify@^1.0.4", "_id": "reusify@1.1.0", "_inBundle": false, "_integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "_location": "/reusify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "reusify@^1.0.4", "name": "reusify", "escapedName": "reusify", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["/fastq"], "_resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "_shasum": "0fe13b9522e1473f51b558ee796e08f11f9b489f", "_spec": "reusify@^1.0.4", "_where": "/mnt/e/www/demo1/node_modules/fastq", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Reuse objects and functions with style", "devDependencies": {"@types/node": "^22.9.0", "c8": "^10.1.2", "eslint": "^9.13.0", "neostandard": "^0.12.0", "pre-commit": "^1.2.2", "tape": "^5.0.0", "typescript": "^5.2.2"}, "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}, "homepage": "https://github.com/mcollina/reusify#readme", "keywords": ["reuse", "object", "performance", "function", "fast"], "license": "MIT", "main": "reusify.js", "name": "reusify", "pre-commit": ["lint", "test", "test:typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/reusify.git"}, "scripts": {"lint": "eslint", "test": "tape test.js", "test:coverage": "c8 --100 tape test.js", "test:typescript": "tsc"}, "types": "reusify.d.ts", "version": "1.1.0"}