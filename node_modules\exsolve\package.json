{"_from": "exsolve@^1.0.7", "_id": "exsolve@1.0.7", "_inBundle": false, "_integrity": "sha512-VO5fQUzZtI6C+vx4w/4BWJpg3s/5l+6pRQEHzFRM8WFi4XffSP1Z+4qi7GbjWbvRQEbdIco5mIMq+zX4rPuLrw==", "_location": "/exsolve", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "exsolve@^1.0.7", "name": "exsolve", "escapedName": "exsolve", "rawSpec": "^1.0.7", "saveSpec": null, "fetchSpec": "^1.0.7"}, "_requiredBy": ["/unimport/local-pkg/pkg-types"], "_resolved": "https://registry.npmjs.org/exsolve/-/exsolve-1.0.7.tgz", "_shasum": "3b74e4c7ca5c5f9a19c3626ca857309fa99f9e9e", "_spec": "exsolve@^1.0.7", "_where": "/mnt/e/www/demo1/node_modules/unimport/node_modules/local-pkg/node_modules/pkg-types", "bugs": {"url": "https://github.com/unjs/exsolve/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Module resolution utilities based on Node.js upstream implementation.", "devDependencies": {"@types/node": "^24.0.3", "@vitest/coverage-v8": "^3.2.4", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.29.0", "eslint-config-unjs": "^0.4.2", "happy-dom": "^18.0.1", "jiti": "^2.4.2", "prettier": "^3.5.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.2.4"}, "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "files": ["dist"], "homepage": "https://github.com/unjs/exsolve#readme", "license": "MIT", "name": "exsolve", "packageManager": "pnpm@10.12.1", "repository": {"type": "git", "url": "git+https://github.com/unjs/exsolve.git"}, "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint . && prettier -c .", "lint:fix": "automd && eslint . --fix && prettier -w .", "node-ts": "node --disable-warning=ExperimentalWarning --experimental-strip-types", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "sideEffects": false, "type": "module", "types": "./dist/index.d.mts", "version": "1.0.7"}