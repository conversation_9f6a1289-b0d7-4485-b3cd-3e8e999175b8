{"_from": "webpack-virtual-modules@^0.6.2", "_id": "webpack-virtual-modules@0.6.2", "_inBundle": false, "_integrity": "sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==", "_location": "/webpack-virtual-modules", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "webpack-virtual-modules@^0.6.2", "name": "webpack-virtual-modules", "escapedName": "webpack-virtual-modules", "rawSpec": "^0.6.2", "saveSpec": null, "fetchSpec": "^0.6.2"}, "_requiredBy": ["/unplugin"], "_resolved": "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "_shasum": "057faa9065c8acf48f24cb57ac0e77739ab9a7e8", "_spec": "webpack-virtual-modules@^0.6.2", "_where": "/mnt/e/www/demo1/node_modules/unplugin", "author": {"name": "SysGears INC"}, "bugs": {"url": "https://github.com/sysgears/webpack-virtual-modules/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Webpack Virtual Modules", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@babel/register": "^7.5.5", "@types/jest": "^24.0.6", "@types/node": "^11.11.3", "@types/tmp": "^0.1.0", "@types/webpack": "^4.32.1", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "babel-jest": "^29.0.3", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memory-fs": "^0.5.0", "prettier": "^2.7.1", "tmp": "^0.2.1", "typescript": "^4.8.3", "webpack": "5"}, "files": ["lib", "src", "!__tests__"], "homepage": "https://github.com/sysgears/webpack-virtual-modules#readme", "husky": {"pre-commit": "lint-staged"}, "keywords": ["webpack", "webpack-plugin", "virtual", "modules"], "license": "MIT", "lint-staged": {"*.ts": ["eslint --fix -c tslint.json", "git add"]}, "main": "lib/index.js", "name": "webpack-virtual-modules", "prettier": {"printWidth": 120, "singleQuote": true, "parser": "typescript"}, "publishConfig": {"main": "lib/index.js", "types": "lib/index.d.ts"}, "repository": {"type": "git", "url": "git+https://github.com/sysgears/webpack-virtual-modules.git"}, "scripts": {"build": "tsc -p tsconfig.build.json", "clean": "rm -rf ./lib", "lint": "eslint --fix src/**/*.ts", "prepack": "yarn clean && yarn build", "test": "yarn lint && yarn tests", "tests": "jest", "tests:watch": "jest --watch", "watch": "tsc -p tsconfig.build.json -w"}, "version": "0.6.2"}