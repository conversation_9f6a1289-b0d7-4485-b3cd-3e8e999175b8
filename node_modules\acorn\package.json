{"_from": "acorn@^8.14.0", "_id": "acorn@8.15.0", "_inBundle": false, "_integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "_location": "/acorn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "acorn@^8.14.0", "name": "acorn", "escapedName": "acorn", "rawSpec": "^8.14.0", "saveSpec": null, "fetchSpec": "^8.14.0"}, "_requiredBy": ["/mlly", "/unimport", "/unplugin"], "_resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "_shasum": "a360898bc415edaac46c8241f6383975b930b816", "_spec": "acorn@^8.14.0", "_where": "/mnt/e/www/demo1/node_modules/mlly", "bin": {"acorn": "bin/acorn"}, "bugs": {"url": "https://github.com/acornjs/acorn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ECMAScript parser", "engines": {"node": ">=0.4.0"}, "exports": {".": [{"import": "./dist/acorn.mjs", "require": "./dist/acorn.js", "default": "./dist/acorn.js"}, "./dist/acorn.js"], "./package.json": "./package.json"}, "homepage": "https://github.com/acornjs/acorn", "license": "MIT", "main": "dist/acorn.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://rreverser.com/"}, {"name": "<PERSON>", "url": "http://adrianheine.de"}], "module": "dist/acorn.mjs", "name": "acorn", "repository": {"type": "git", "url": "git+https://github.com/acornjs/acorn.git"}, "scripts": {"prepare": "cd ..; npm run build:main"}, "types": "dist/acorn.d.ts", "version": "8.15.0"}