<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复原因对话框测试 - 数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-comment-dots text-primary"></i>
                    修复原因对话框测试页面
                </h1>
                <div class="text-center mb-4">
                    <a href="index.html" class="btn btn-primary me-2">
                        <i class="fas fa-home"></i> 返回主页
                    </a>
                    <a href="test-node-operation.html" class="btn btn-info">
                        <i class="fas fa-cogs"></i> Node操作测试
                    </a>
                </div>
            </div>
        </div>

        <!-- 修复原因对话框测试 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-edit"></i> 修复原因对话框功能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-1 text-primary"></i> 首次点击测试</h6>
                                <p class="text-muted">首次点击开始修复按钮会弹出修复原因对话框</p>
                                <button class="btn btn-primary me-2" onclick="testRepairDialog('quick')">
                                    <i class="fas fa-play"></i> 测试快速修复
                                </button>
                                <button class="btn btn-success" onclick="testRepairDialog('batch')">
                                    <i class="fas fa-play"></i> 测试批量修复
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        点击按钮会弹出修复原因填写对话框
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-2 text-success"></i> 后续点击测试</h6>
                                <p class="text-muted">填写原因后，后续点击直接执行，无需重新填写</p>
                                <div class="alert alert-info">
                                    <strong>当前修复原因：</strong>
                                    <span id="currentReason" class="text-primary">未填写</span>
                                </div>
                                <button class="btn btn-outline-secondary" onclick="clearRepairReason()">
                                    <i class="fas fa-eraser"></i> 清除修复原因
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        清除后再次点击会重新弹出对话框
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表格样式测试 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> 优化后的表格样式测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>改进说明：</strong>移除了表格内的所有删除按钮，改为表格左侧的外部删除按钮，鼠标悬停在行上时显示。
                        </div>
                        
                        <!-- 表格容器 -->
                        <div class="table-container-wrapper">
                            <div class="table-container">
                                <div class="table-responsive">
                                    <table class="table excel-table" id="testTable">
                                        <thead>
                                            <tr>
                                                <th contenteditable="true">channel</th>
                                                <th contenteditable="true">sellerId</th>
                                                <th contenteditable="true">skuId</th>
                                                <th contenteditable="true">mskuId</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="editable" contenteditable="true">amazon</td>
                                                <td class="editable" contenteditable="true">seller001</td>
                                                <td class="editable" contenteditable="true">SKU123</td>
                                                <td class="editable" contenteditable="true">MSKU456</td>
                                            </tr>
                                            <tr>
                                                <td class="editable" contenteditable="true">ebay</td>
                                                <td class="editable" contenteditable="true">seller002</td>
                                                <td class="editable" contenteditable="true">SKU789</td>
                                                <td class="editable" contenteditable="true">MSKU012</td>
                                            </tr>
                                            <tr>
                                                <td class="editable" contenteditable="true">shopify</td>
                                                <td class="editable" contenteditable="true">seller003</td>
                                                <td class="editable" contenteditable="true">SKU345</td>
                                                <td class="editable" contenteditable="true">MSKU678</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="add-column-container">
                                <button class="btn btn-outline-primary btn-sm" onclick="addTestColumn()">
                                    <i class="fas fa-plus"></i> 新增列
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-actions mt-3">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="addTestRow()">
                                <i class="fas fa-plus"></i> 新增行
                            </button>
                            <button class="btn btn-outline-danger btn-sm me-2" onclick="clearTestTable()">
                                <i class="fas fa-trash"></i> 清空表格
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="testPasteData()">
                                <i class="fas fa-paste"></i> 测试粘贴数据
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <h6>测试说明：</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>鼠标悬停在表格行上会在左侧显示红色删除按钮</li>
                                <li><i class="fas fa-check text-success me-2"></i>删除按钮位于表格外部，不影响单元格内容</li>
                                <li><i class="fas fa-check text-success me-2"></i>粘贴Excel数据时自动扩展行列，样式统一</li>
                                <li><i class="fas fa-check text-success me-2"></i>新增行列操作后自动更新删除按钮位置</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能对比 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-balance-scale"></i> 改进对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>改进前的问题</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        每次点击都需要填写修复原因
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        表格内删除按钮影响单元格显示
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        粘贴数据后样式不统一
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        删除按钮过多，界面混乱
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>改进后的优势</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        修复原因全局保存，无需重复填写
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        外部删除按钮，不影响单元格内容
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        粘贴数据后自动应用统一样式
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        悬停显示，界面更加简洁
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        支持回车键快速确认对话框
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 测试修复原因对话框
        function testRepairDialog(type) {
            showRepairReasonDialog(type);
        }
        
        // 清除修复原因
        function clearRepairReason() {
            repairReason = '';
            updateReasonDisplay();
        }
        
        // 更新原因显示
        function updateReasonDisplay() {
            const reasonSpan = document.getElementById('currentReason');
            if (reasonSpan) {
                reasonSpan.textContent = repairReason || '未填写';
            }
        }
        
        // 监听修复原因变化
        setInterval(updateReasonDisplay, 1000);
        
        // 测试表格功能
        function addTestColumn() {
            addTableColumn('testTable');
        }
        
        function addTestRow() {
            addTableRow('testTable');
        }
        
        function clearTestTable() {
            clearTable('testTable');
        }
        
        function testPasteData() {
            // 模拟粘贴数据
            const testData = 'walmart\tseller004\tSKU999\tMSKU111\nwish\tseller005\tSKU888\tMSKU222';
            const table = document.getElementById('testTable');
            const tbody = table.querySelector('tbody');
            
            // 清空现有数据
            tbody.innerHTML = '';
            
            // 解析测试数据
            const rows = testData.split('\n');
            rows.forEach(rowData => {
                const cells = rowData.split('\t');
                const row = document.createElement('tr');
                
                cells.forEach(cellData => {
                    const td = document.createElement('td');
                    td.className = 'editable';
                    td.contentEditable = true;
                    td.textContent = cellData;
                    row.appendChild(td);
                });
                
                tbody.appendChild(row);
            });
            
            updateRowDeleteButtons();
            alert('已模拟粘贴测试数据，观察样式是否统一');
        }
        
        // 页面加载时初始化删除按钮
        document.addEventListener('DOMContentLoaded', function() {
            updateRowDeleteButtons();
        });
    </script>
</body>
</html>
