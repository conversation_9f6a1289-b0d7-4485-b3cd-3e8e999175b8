{"_from": "@rollup/rollup-linux-x64-gnu@4.45.1", "_id": "@rollup/rollup-linux-x64-gnu@4.45.1", "_inBundle": false, "_integrity": "sha512-+E/lYl6qu1zqgPEnTrs4WysQtvc/Sh4fC2nByfFExqgYrqkKWp1tWIbe+ELhixnenSpBbLXNi6vbEEJ8M7fiHw==", "_location": "/@rollup/rollup-linux-x64-gnu", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@rollup/rollup-linux-x64-gnu@4.45.1", "name": "@rollup/rollup-linux-x64-gnu", "escapedName": "@rollup%2frollup-linux-x64-gnu", "scope": "@rollup", "rawSpec": "4.45.1", "saveSpec": null, "fetchSpec": "4.45.1"}, "_requiredBy": ["/rollup"], "_resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.45.1.tgz", "_shasum": "0d4c8d0b8f801902f0844a40a9d981a0179f4971", "_spec": "@rollup/rollup-linux-x64-gnu@4.45.1", "_where": "/mnt/e/www/demo1/node_modules/rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "Native bindings for Rollup", "files": ["rollup.linux-x64-gnu.node"], "homepage": "https://rollupjs.org/", "libc": ["glibc"], "license": "MIT", "main": "./rollup.linux-x64-gnu.node", "name": "@rollup/rollup-linux-x64-gnu", "os": ["linux"], "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "version": "4.45.1"}