{"_from": "@babel/types@^7.28.0", "_id": "@babel/types@7.28.1", "_inBundle": false, "_integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==", "_location": "/@babel/types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/types@^7.28.0", "name": "@babel/types", "escapedName": "@babel%2ftypes", "scope": "@babel", "rawSpec": "^7.28.0", "saveSpec": null, "fetchSpec": "^7.28.0"}, "_requiredBy": ["/@babel/parser"], "_resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "_shasum": "2aaf3c10b31ba03a77ac84f52b3912a0edef4cf9", "_spec": "@babel/types@^7.28.0", "_where": "/mnt/e/www/demo1/node_modules/@babel/parser", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "deprecated": false, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "devDependencies": {"@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "glob": "^7.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-types", "license": "MIT", "main": "./lib/index.js", "name": "@babel/types", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "type": "commonjs", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "version": "7.28.1"}