{"_from": "@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7", "_id": "@sxzz/popperjs-es@2.11.7", "_inBundle": false, "_integrity": "sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==", "_location": "/@popperjs/core", "_phantomChildren": {}, "_requested": {"type": "alias", "registry": true, "raw": "@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7", "name": "@popperjs/core", "escapedName": "@popperjs%2fcore", "scope": "@popperjs", "rawSpec": "npm:@sxzz/popperjs-es@^2.11.7", "saveSpec": null, "fetchSpec": null, "subSpec": {"type": "range", "registry": true, "raw": "@sxzz/popperjs-es@^2.11.7", "name": "@sxzz/popperjs-es", "escapedName": "@sxzz%2fpopperjs-es", "scope": "@sxzz", "rawSpec": "^2.11.7", "saveSpec": null, "fetchSpec": "^2.11.7"}}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz", "_shasum": "a7f69e3665d3da9b115f9e71671dae1b97e13671", "_spec": "@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "babel": {"extends": "./.config/babel.config"}, "bugs": {"url": "https://github.com/popperjs/popper-core/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tooltip and Popover Positioning Engine", "devDependencies": {"@ampproject/rollup-plugin-closure-compiler": "^0.26.0", "@atomico/rollup-plugin-sizes": "^1.1.4", "@babel/cli": "^7.12.17", "@babel/core": "^7.12.17", "@babel/plugin-transform-flow-strip-types": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.17", "@babel/preset-env": "^7.12.17", "@fezvrasta/tsc-silent": "^1.3.0", "@khanacademy/flow-to-ts": "^0.3.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-replace": "^2.3.4", "babel-eslint": "^10.0.3", "babel-jest": "^26.6.3", "babel-plugin-add-import-extension": "^1.4.4", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-dev-expression": "^0.2.2", "babel-plugin-inline-replace-variables": "^1.3.1", "babel-plugin-transform-inline-environment-variables": "^0.4.3", "concurrently": "^5.3.0", "dotenv": "^8.2.0", "esbuild": "^0.14.38", "esbuild-plugin-flow": "^0.3.2", "eslint": "^7.20.0", "eslint-plugin-flowtype": "^5.2.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-unused-imports": "^1.1.0", "esno": "^0.14.1", "flow-bin": "^0.139.0", "flow-copy-source": "^2.0.9", "get-port-cli": "^2.0.0", "husky": "^5.0.9", "jest": "^26.6.3", "jest-environment-jsdom-sixteen": "^1.0.3", "jest-environment-puppeteer": "^4.4.0", "jest-image-snapshot": "^4.3.0", "jest-puppeteer": "^4.4.0", "pinst": "^2.1.4", "poster": "^0.0.9", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "puppeteer": "^10.4.0", "replace-in-files-cli": "^1.0.0", "rollup": "^2.70.2", "rollup-plugin-esbuild": "^4.9.1", "rollup-plugin-flow-entry": "^0.3.3", "rollup-plugin-license": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^4.2.0", "serve": "^11.3.2", "typescript": "^4.1.5"}, "eslintConfig": {"extends": "./.config/eslint.config"}, "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs"}, "./*": "./*"}, "files": ["index.d.ts", "/dist", "/lib"], "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}, "homepage": "https://github.com/popperjs/popper-core#readme", "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "jest": {"preset": "./.config/jest.config"}, "keywords": ["tooltip", "popover", "dropdown", "popup", "popper", "positioning engine"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "name": "@sxzz/popperjs-es", "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "proseWrap": "always"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/popperjs/popper-core.git"}, "scripts": {"build": "yarn clean && esmo build/build.ts && pnpm run build:typescript", "build:typescript": "rimraf dist/typescript; flow-to-ts \"src/**/*.js\" --write --inline-utility-types; tsc-silent --project .config/tsconfig.json --createSourceFile .config/createSourceFile.js --suppress @; rimraf \"src/**/*.ts\"", "clean": "rimraf lib && rimraf dist && rimraf test/visual/dist", "dev": "NODE_ENV=dev concurrently 'yarn serve' 'yarn build:dev --watch'", "serve": "serve -l ${DEV_PORT:-5000} tests/visual", "test": "yarn test:unit && yarn test:functional", "test:eslint": "eslint .", "test:flow": "flow", "test:functional": "DEV_PORT=`get-port` jest tests/functional", "test:typescript": "tsc --project tests/typescript/tsconfig.json", "test:unit": "jest --coverage src"}, "sideEffects": false, "types": "./index.d.ts", "unpkg": "dist/index.iife.js", "version": "2.11.7"}