<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-tools text-primary"></i>
                    数据修复工具
                </h1>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs" id="repairTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="user-tab" data-bs-toggle="tab" data-bs-target="#user-pane" 
                                type="button" role="tab" aria-controls="user-pane" aria-selected="true">
                            <i class="fas fa-user"></i> 用户数据修复
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order-pane" 
                                type="button" role="tab" aria-controls="order-pane" aria-selected="false">
                            <i class="fas fa-shopping-cart"></i> 订单数据修复
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="product-tab" data-bs-toggle="tab" data-bs-target="#product-pane" 
                                type="button" role="tab" aria-controls="product-pane" aria-selected="false">
                            <i class="fas fa-box"></i> 商品数据修复
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="repairTabsContent">
            <!-- 用户数据修复标签页 -->
            <div class="tab-pane fade show active" id="user-pane" role="tabpanel" aria-labelledby="user-tab">
                <div class="row mt-3">
                    <div class="col-12">
                        <!-- 参数配置表单 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5><i class="fas fa-cog"></i> 参数配置</h5>
                            </div>
                            <div class="card-body">
                                <form id="userConfigForm">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="userTableName" class="form-label">表名</label>
                                            <input type="text" class="form-control" id="userTableName" value="users" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="userAction" class="form-label">更新动作</label>
                                            <select class="form-select" id="userAction" required>
                                                <option value="update">更新</option>
                                                <option value="insert">插入</option>
                                                <option value="delete">删除</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="userQueryField" class="form-label">查询字段</label>
                                            <input type="text" class="form-control" id="userQueryField" value="user_id" required>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速修复功能 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-table"></i> 快速修复功能</h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableRow('quickRepairTable')">
                                        <i class="fas fa-plus"></i> 添加行
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableColumn('quickRepairTable')">
                                        <i class="fas fa-plus"></i> 添加列
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearTable('quickRepairTable')">
                                        <i class="fas fa-trash"></i> 清空
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportTable('quickRepairTable')">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="quickRepairTable">
                                        <!-- 表格内容将通过JavaScript动态生成 -->
                                    </table>
                                </div>
                                <div class="d-flex align-items-center mt-3">
                                    <label for="quickThreads" class="form-label me-2">线程数:</label>
                                    <input type="number" class="form-control me-3" id="quickThreads" value="10" min="1" max="100" style="width: 100px;">
                                    <button type="button" class="btn btn-primary" onclick="startQuickRepair('user')">
                                        <i class="fas fa-play"></i> 开始修复
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 大批量修复功能 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-csv"></i> 大批量修复功能</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="csvFile" class="form-label">选择CSV文件</label>
                                    <input type="file" class="form-control" id="csvFile" accept=".csv" onchange="handleCSVFile(event, 'user')">
                                </div>
                                <div id="csvPreview" class="mb-3" style="display: none;">
                                    <h6>CSV预览:</h6>
                                    <div class="table-responsive" style="max-height: 200px;">
                                        <table class="table table-sm table-bordered" id="csvPreviewTable">
                                        </table>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <label for="batchThreads" class="form-label me-2">线程数:</label>
                                    <input type="number" class="form-control me-3" id="batchThreads" value="50" min="1" max="200" style="width: 100px;">
                                    <button type="button" class="btn btn-primary" onclick="startBatchRepair('user')" disabled id="batchStartBtn">
                                        <i class="fas fa-play"></i> 开始修复
                                    </button>
                                </div>
                                <!-- 进度显示 -->
                                <div id="batchProgress" class="mt-3" style="display: none;">
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">0%</div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <small class="text-muted">已处理/总数</small><br>
                                            <span id="processedCount">0</span> / <span id="totalCount">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">处理时长</small><br>
                                            <span id="elapsedTime">0.00秒</span>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">预计剩余</small><br>
                                            <span id="remainingTime">--</span>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">总耗时</small><br>
                                            <span id="totalTime">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单数据修复标签页 -->
            <div class="tab-pane fade" id="order-pane" role="tabpanel" aria-labelledby="order-tab">
                <!-- 订单修复内容将通过JavaScript动态生成 -->
            </div>

            <!-- 商品数据修复标签页 -->
            <div class="tab-pane fade" id="product-pane" role="tabpanel" aria-labelledby="product-tab">
                <!-- 商品修复内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>
