<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-tools text-primary"></i>
                    数据修复工具
                </h1>
                <div class="text-center mb-3">
                    <a href="demo.html" class="btn btn-outline-info me-2">
                        <i class="fas fa-magic"></i> 功能演示
                    </a>
                    <a href="test.html" class="btn btn-outline-secondary">
                        <i class="fas fa-vial"></i> 测试页面
                    </a>
                </div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs flex-wrap" id="repairTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="user-tab" data-bs-toggle="tab" data-bs-target="#user-pane"
                                type="button" role="tab" aria-controls="user-pane" aria-selected="true">
                            <i class="fas fa-user"></i> 用户
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order-pane"
                                type="button" role="tab" aria-controls="order-pane" aria-selected="false">
                            <i class="fas fa-shopping-cart"></i> 订单
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="product-tab" data-bs-toggle="tab" data-bs-target="#product-pane"
                                type="button" role="tab" aria-controls="product-pane" aria-selected="false">
                            <i class="fas fa-box"></i> 商品
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer-pane"
                                type="button" role="tab" aria-controls="customer-pane" aria-selected="false">
                            <i class="fas fa-users"></i> 客户
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment-pane"
                                type="button" role="tab" aria-controls="payment-pane" aria-selected="false">
                            <i class="fas fa-credit-card"></i> 支付
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="category-tab" data-bs-toggle="tab" data-bs-target="#category-pane"
                                type="button" role="tab" aria-controls="category-pane" aria-selected="false">
                            <i class="fas fa-tags"></i> 分类
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory-pane"
                                type="button" role="tab" aria-controls="inventory-pane" aria-selected="false">
                            <i class="fas fa-warehouse"></i> 库存
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="supplier-tab" data-bs-toggle="tab" data-bs-target="#supplier-pane"
                                type="button" role="tab" aria-controls="supplier-pane" aria-selected="false">
                            <i class="fas fa-truck"></i> 供应商
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="coupon-tab" data-bs-toggle="tab" data-bs-target="#coupon-pane"
                                type="button" role="tab" aria-controls="coupon-pane" aria-selected="false">
                            <i class="fas fa-ticket-alt"></i> 优惠券
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="review-tab" data-bs-toggle="tab" data-bs-target="#review-pane"
                                type="button" role="tab" aria-controls="review-pane" aria-selected="false">
                            <i class="fas fa-star"></i> 评价
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="shipping-tab" data-bs-toggle="tab" data-bs-target="#shipping-pane"
                                type="button" role="tab" aria-controls="shipping-pane" aria-selected="false">
                            <i class="fas fa-shipping-fast"></i> 物流
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="log-tab" data-bs-toggle="tab" data-bs-target="#log-pane"
                                type="button" role="tab" aria-controls="log-pane" aria-selected="false">
                            <i class="fas fa-list-alt"></i> 日志
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config-pane"
                                type="button" role="tab" aria-controls="config-pane" aria-selected="false">
                            <i class="fas fa-cog"></i> 配置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="report-tab" data-bs-toggle="tab" data-bs-target="#report-pane"
                                type="button" role="tab" aria-controls="report-pane" aria-selected="false">
                            <i class="fas fa-chart-bar"></i> 报表
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup-pane"
                                type="button" role="tab" aria-controls="backup-pane" aria-selected="false">
                            <i class="fas fa-database"></i> 备份
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="repairTabsContent">
            <!-- 共享的快速修复和大批量修复区域 -->
            <div class="row mt-3">
                <div class="col-12">
                    <!-- 参数配置表单 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5><i class="fas fa-cog"></i> 参数配置</h5>
                        </div>
                        <div class="card-body">
                            <form id="configForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="tableName" class="form-label">表名</label>
                                        <input type="text" class="form-control" id="tableName" value="users" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="action" class="form-label">更新动作</label>
                                        <select class="form-select" id="action" required>
                                            <option value="update">更新</option>
                                            <option value="insert">插入</option>
                                            <option value="delete">删除</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="queryField" class="form-label">查询字段</label>
                                        <input type="text" class="form-control" id="queryField" value="user_id" required>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速修复功能 -->
            <div class="row">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-table"></i> 快速修复功能</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableRow('sharedQuickRepairTable')">
                                    <i class="fas fa-plus"></i> 添加行
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableColumn('sharedQuickRepairTable')">
                                    <i class="fas fa-plus"></i> 添加列
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearTable('sharedQuickRepairTable')">
                                    <i class="fas fa-trash"></i> 清空
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportTable('sharedQuickRepairTable')">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <strong>提示：</strong>您可以直接从Excel复制数据并粘贴到表格中，系统会自动调整行列数量。切换标签页时表格数据保持不变。
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="sharedQuickRepairTable">
                                    <!-- 表格内容将通过JavaScript动态生成 -->
                                </table>
                            </div>
                            <div class="d-flex align-items-center mt-3">
                                <label for="quickThreads" class="form-label me-2">线程数:</label>
                                <input type="number" class="form-control me-3" id="quickThreads" value="10" min="1" max="100" style="width: 100px;">
                                <button type="button" class="btn btn-primary" onclick="startQuickRepair()" id="quickStartBtn">
                                    <i class="fas fa-play"></i> 开始修复
                                </button>
                            </div>
                            <!-- 快速修复进度显示 -->
                            <div id="quickProgress" class="mt-3" style="display: none;">
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 0%" id="quickProgressBar">0%</div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <small class="text-muted">已处理/总数</small><br>
                                        <span id="quickProcessedCount">0</span> / <span id="quickTotalCount">0</span>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">处理时长</small><br>
                                        <span id="quickElapsedTime">0.00秒</span>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">成功/失败</small><br>
                                        <span id="quickSuccessCount" class="text-success">0</span> / <span id="quickErrorCount" class="text-danger">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大批量修复功能 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-csv"></i> 大批量修复功能</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="csvFile" class="form-label">选择CSV文件</label>
                                <input type="file" class="form-control" id="csvFile" accept=".csv" onchange="handleCSVFile(event)">
                            </div>
                            <div id="csvPreview" class="mb-3" style="display: none;">
                                <h6>CSV预览:</h6>
                                <div class="table-responsive" style="max-height: 200px;">
                                    <table class="table table-sm table-bordered" id="csvPreviewTable">
                                    </table>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <label for="batchThreads" class="form-label me-2">线程数:</label>
                                <input type="number" class="form-control me-3" id="batchThreads" value="50" min="1" max="200" style="width: 100px;">
                                <button type="button" class="btn btn-primary" onclick="startBatchRepair()" disabled id="batchStartBtn">
                                    <i class="fas fa-play"></i> 开始修复
                                </button>
                                <button type="button" class="btn btn-success ms-2" onclick="exportBatchResults()" disabled id="exportResultsBtn">
                                    <i class="fas fa-download"></i> 导出结果
                                </button>
                            </div>
                            <!-- 进度显示 -->
                            <div id="batchProgress" class="mt-3" style="display: none;">
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">0%</div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <small class="text-muted">已处理/总数</small><br>
                                        <span id="processedCount">0</span> / <span id="totalCount">0</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">处理时长</small><br>
                                        <span id="elapsedTime">0.00秒</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">预计剩余</small><br>
                                        <span id="remainingTime">--</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">总耗时</small><br>
                                        <span id="totalTime">--</span>
                                    </div>
                                </div>
                                <!-- 处理结果摘要 -->
                                <div id="batchSummary" class="mt-3" style="display: none;">
                                    <div class="alert" role="alert" id="summaryAlert">
                                        <!-- 处理结果摘要将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签页指示器 -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle text-info"></i>
                                当前选择的数据类型：<span id="currentTabIndicator" class="badge bg-primary">用户</span>
                            </h6>
                            <small class="text-muted">切换上方标签页可更改数据类型和配置参数</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 隐藏的标签页内容容器 -->
            <div class="tab-pane fade" id="order-pane" role="tabpanel" aria-labelledby="order-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="product-pane" role="tabpanel" aria-labelledby="product-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="customer-pane" role="tabpanel" aria-labelledby="customer-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="payment-pane" role="tabpanel" aria-labelledby="payment-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="category-pane" role="tabpanel" aria-labelledby="category-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="inventory-pane" role="tabpanel" aria-labelledby="inventory-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="supplier-pane" role="tabpanel" aria-labelledby="supplier-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="coupon-pane" role="tabpanel" aria-labelledby="coupon-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="review-pane" role="tabpanel" aria-labelledby="review-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="shipping-pane" role="tabpanel" aria-labelledby="shipping-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="log-pane" role="tabpanel" aria-labelledby="log-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="config-pane" role="tabpanel" aria-labelledby="config-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="report-pane" role="tabpanel" aria-labelledby="report-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="backup-pane" role="tabpanel" aria-labelledby="backup-tab" style="display: none;"></div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toast 消息将动态添加到这里 -->
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>
