<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-tools text-primary"></i>
                    数据修复工具
                </h1>
                <div class="text-center mb-3">
                    <a href="demo.html" class="btn btn-outline-info me-2">
                        <i class="fas fa-magic"></i> 功能演示
                    </a>
                    <a href="test.html" class="btn btn-outline-secondary">
                        <i class="fas fa-vial"></i> 测试页面
                    </a>
                </div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs flex-wrap" id="repairTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="node-tab" data-bs-toggle="tab" data-bs-target="#node-pane"
                                type="button" role="tab" aria-controls="node-pane" aria-selected="true">
                            <i class="fas fa-cogs"></i> node操作
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="user-tab" data-bs-toggle="tab" data-bs-target="#user-pane"
                                type="button" role="tab" aria-controls="user-pane" aria-selected="false">
                            <i class="fas fa-user"></i> 用户
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order-pane"
                                type="button" role="tab" aria-controls="order-pane" aria-selected="false">
                            <i class="fas fa-shopping-cart"></i> 订单
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="product-tab" data-bs-toggle="tab" data-bs-target="#product-pane"
                                type="button" role="tab" aria-controls="product-pane" aria-selected="false">
                            <i class="fas fa-box"></i> 商品
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer-pane"
                                type="button" role="tab" aria-controls="customer-pane" aria-selected="false">
                            <i class="fas fa-users"></i> 客户
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment-pane"
                                type="button" role="tab" aria-controls="payment-pane" aria-selected="false">
                            <i class="fas fa-credit-card"></i> 支付
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="category-tab" data-bs-toggle="tab" data-bs-target="#category-pane"
                                type="button" role="tab" aria-controls="category-pane" aria-selected="false">
                            <i class="fas fa-tags"></i> 分类
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory-pane"
                                type="button" role="tab" aria-controls="inventory-pane" aria-selected="false">
                            <i class="fas fa-warehouse"></i> 库存
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="supplier-tab" data-bs-toggle="tab" data-bs-target="#supplier-pane"
                                type="button" role="tab" aria-controls="supplier-pane" aria-selected="false">
                            <i class="fas fa-truck"></i> 供应商
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="coupon-tab" data-bs-toggle="tab" data-bs-target="#coupon-pane"
                                type="button" role="tab" aria-controls="coupon-pane" aria-selected="false">
                            <i class="fas fa-ticket-alt"></i> 优惠券
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="review-tab" data-bs-toggle="tab" data-bs-target="#review-pane"
                                type="button" role="tab" aria-controls="review-pane" aria-selected="false">
                            <i class="fas fa-star"></i> 评价
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="shipping-tab" data-bs-toggle="tab" data-bs-target="#shipping-pane"
                                type="button" role="tab" aria-controls="shipping-pane" aria-selected="false">
                            <i class="fas fa-shipping-fast"></i> 物流
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="log-tab" data-bs-toggle="tab" data-bs-target="#log-pane"
                                type="button" role="tab" aria-controls="log-pane" aria-selected="false">
                            <i class="fas fa-list-alt"></i> 日志
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config-pane"
                                type="button" role="tab" aria-controls="config-pane" aria-selected="false">
                            <i class="fas fa-cog"></i> 配置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="report-tab" data-bs-toggle="tab" data-bs-target="#report-pane"
                                type="button" role="tab" aria-controls="report-pane" aria-selected="false">
                            <i class="fas fa-chart-bar"></i> 报表
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup-pane"
                                type="button" role="tab" aria-controls="backup-pane" aria-selected="false">
                            <i class="fas fa-database"></i> 备份
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="repairTabsContent">
            <!-- 共享的快速修复和大批量修复区域 -->
            <div class="row mt-3">
                <div class="col-12">
                    <!-- 参数配置表单 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5><i class="fas fa-cog"></i> 参数配置</h5>
                        </div>
                        <div class="card-body">
                            <form id="configForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="tableName" class="form-label">表名</label>
                                        <select class="form-select" id="tableName" required>
                                            <option value="">请选择表名</option>
                                            <!-- 表名选项将通过JavaScript动态添加 -->
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="action" class="form-label">动作</label>
                                        <select class="form-select" id="action" required onchange="handleActionChange()">
                                            <option value="">请选择动作</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6" id="additionalFieldContainer">
                                        <!-- 动态字段将在这里显示 -->
                                    </div>
                                </div>
                                <!-- 更新操作的额外字段 -->
                                <div class="row mt-3" id="updateFields" style="display: none;">
                                    <div class="col-md-6">
                                        <label for="queryConditions" class="form-label">查询条件</label>
                                        <input type="text" class="form-control" id="queryConditions" placeholder="可填写多个字段，并用逗号隔开">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="updateFields" class="form-label">更新字段</label>
                                        <input type="text" class="form-control" id="updateFieldsList" placeholder="可填写多个字段，并用逗号隔开">
                                    </div>
                                </div>
                                <!-- 删除操作的额外字段 -->
                                <div class="row mt-3" id="deleteFields" style="display: none;">
                                    <div class="col-md-6">
                                        <label for="deleteConditions" class="form-label">条件删除</label>
                                        <input type="text" class="form-control" id="deleteConditions" placeholder="根据查询字段条件删除记录行">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速修复功能 -->
            <div class="row">
                <div class="col-12">
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5><i class="fas fa-table"></i> 快速修复功能</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <strong>提示：</strong>您可以直接从Excel复制数据并粘贴到表格中，系统会自动调整行列数量。鼠标悬停在行上可显示删除按钮。
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>

                            <!-- 表格容器 -->
                            <div class="table-container-wrapper">
                                <div class="table-container">
                                    <div class="table-responsive">
                                        <table class="table editable-table" id="sharedQuickRepairTable">
                                            <!-- 表格内容将通过JavaScript动态生成 -->
                                        </table>
                                    </div>
                                </div>
                                <!-- 添加列按钮 -->
                                <div class="add-column-container">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTableColumn('sharedQuickRepairTable')" title="添加列">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 表格下方操作区域 -->
                            <div class="table-actions mt-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="addTableRow('sharedQuickRepairTable')">
                                            <i class="fas fa-plus"></i> 添加行
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm me-2" onclick="clearTable('sharedQuickRepairTable')">
                                            <i class="fas fa-trash"></i> 清空表格
                                        </button>
                                    </div>
                                    <div class="current-tab-indicator">
                                        <i class="fas fa-tag"></i>
                                        当前数据类型：<span id="currentTabIndicator" class="ms-1">node操作</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="d-flex align-items-center">
                                    <label for="quickThreads" class="form-label me-2 mb-0">线程数:</label>
                                    <input type="number" class="form-control me-3" id="quickThreads" value="10" min="1" max="100" style="width: 100px;">
                                    <button type="button" class="btn btn-primary me-3" onclick="showRepairReasonDialog('quick')" id="quickStartBtn">
                                        <i class="fas fa-play"></i> 开始修复
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="exportQuickResults()" id="exportQuickResultsBtn" disabled>
                                        <i class="fas fa-download"></i> 导出结果
                                    </button>
                                </div>
                            </div>
                            <!-- 快速修复进度显示 -->
                            <div id="quickProgress" class="mt-3" style="display: none;">
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 0%" id="quickProgressBar">0%</div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <small class="text-muted">已处理/总数</small><br>
                                        <span id="quickProcessedCount">0</span> / <span id="quickTotalCount">0</span>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">处理时长</small><br>
                                        <span id="quickElapsedTime">0.00秒</span>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">成功/失败</small><br>
                                        <span id="quickSuccessCount" class="text-success">0</span> / <span id="quickErrorCount" class="text-danger">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大批量修复功能 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-csv"></i> 大批量修复功能</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="csvFile" class="form-label">选择CSV文件</label>
                                <input type="file" class="form-control" id="csvFile" accept=".csv" onchange="handleCSVFile(event)">
                            </div>
                            <div id="csvPreview" class="csv-preview-container" style="display: none;">
                                <div class="csv-preview-header">
                                    <h6 class="csv-preview-title">
                                        <i class="fas fa-eye me-2"></i>CSV文件预览
                                    </h6>
                                    <span class="csv-preview-info" id="csvPreviewInfo">
                                        显示前10行数据
                                    </span>
                                </div>
                                <div class="csv-preview-table-wrapper">
                                    <table class="csv-preview-table" id="csvPreviewTable">
                                    </table>
                                </div>
                            </div>
                            <div class="d-flex align-items-center flex-wrap gap-2">
                                <label for="batchThreads" class="form-label me-2 mb-0">线程数:</label>
                                <input type="number" class="form-control me-2" id="batchThreads" value="50" min="1" max="200" style="width: 100px;">
                                <button type="button" class="btn btn-primary me-2" onclick="showRepairReasonDialog('batch')" disabled id="batchStartBtn">
                                    <i class="fas fa-play"></i> 开始修复
                                </button>
                                <button type="button" class="btn btn-success" onclick="exportBatchResults()" disabled id="exportResultsBtn">
                                    <i class="fas fa-download"></i> 导出结果
                                </button>
                            </div>
                            <!-- 进度显示 -->
                            <div id="batchProgress" class="mt-3" style="display: none;">
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">0%</div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <small class="text-muted">已处理/总数</small><br>
                                        <span id="processedCount">0</span> / <span id="totalCount">0</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">处理时长</small><br>
                                        <span id="elapsedTime">0.00秒</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">预计剩余</small><br>
                                        <span id="remainingTime">--</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">总耗时</small><br>
                                        <span id="totalTime">--</span>
                                    </div>
                                </div>
                                <!-- 处理结果摘要 -->
                                <div id="batchSummary" class="mt-3" style="display: none;">
                                    <div class="alert" role="alert" id="summaryAlert">
                                        <!-- 处理结果摘要将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 隐藏的标签页内容容器 -->
            <div class="tab-pane fade" id="node-pane" role="tabpanel" aria-labelledby="node-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="user-pane" role="tabpanel" aria-labelledby="user-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="order-pane" role="tabpanel" aria-labelledby="order-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="product-pane" role="tabpanel" aria-labelledby="product-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="customer-pane" role="tabpanel" aria-labelledby="customer-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="payment-pane" role="tabpanel" aria-labelledby="payment-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="category-pane" role="tabpanel" aria-labelledby="category-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="inventory-pane" role="tabpanel" aria-labelledby="inventory-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="supplier-pane" role="tabpanel" aria-labelledby="supplier-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="coupon-pane" role="tabpanel" aria-labelledby="coupon-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="review-pane" role="tabpanel" aria-labelledby="review-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="shipping-pane" role="tabpanel" aria-labelledby="shipping-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="log-pane" role="tabpanel" aria-labelledby="log-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="config-pane" role="tabpanel" aria-labelledby="config-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="report-pane" role="tabpanel" aria-labelledby="report-tab" style="display: none;"></div>
            <div class="tab-pane fade" id="backup-pane" role="tabpanel" aria-labelledby="backup-tab" style="display: none;"></div>
        </div>
    </div>

    <!-- 修复原因对话框 -->
    <div class="modal fade" id="repairReasonModal" tabindex="-1" aria-labelledby="repairReasonModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="repairReasonModalLabel">
                        <i class="fas fa-edit text-warning"></i>
                        填写修复原因
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>重要提醒：</strong>请详细填写修复原因，包括操作人员、日期和具体原因，此信息将用于操作审计。
                    </div>
                    <form id="repairReasonForm">
                        <div class="mb-3">
                            <label for="repairReason" class="form-label">修复原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="repairReason" rows="4" required
                                placeholder="请详细描述修复原因，例如：&#10;操作人：张三&#10;日期：2024-07-19&#10;原因：修复用户数据异常问题，订单ID 12345 状态错误"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="operatorName" class="form-label">操作人员 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="operatorName" required placeholder="请输入操作人员姓名">
                        </div>
                        <div class="mb-3">
                            <label for="operationDate" class="form-label">操作日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="operationDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmRepairAction()" id="confirmRepairBtn">
                        <i class="fas fa-check"></i> 确认开始修复
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toast 消息将动态添加到这里 -->
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>
