{"_from": "@floating-ui/core@^1.7.2", "_id": "@floating-ui/core@1.7.2", "_inBundle": false, "_integrity": "sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==", "_location": "/@floating-ui/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@floating-ui/core@^1.7.2", "name": "@floating-ui/core", "escapedName": "@floating-ui%2fcore", "scope": "@floating-ui", "rawSpec": "^1.7.2", "saveSpec": null, "fetchSpec": "^1.7.2"}, "_requiredBy": ["/@floating-ui/dom"], "_resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz", "_shasum": "3d1c35263950b314b6d5a72c8bfb9e3c1551aefd", "_spec": "@floating-ui/core@^1.7.2", "_where": "/mnt/e/www/demo1/node_modules/@floating-ui/dom", "author": {"name": "atomiks"}, "bugs": {"url": "https://github.com/floating-ui/floating-ui"}, "bundleDependencies": false, "dependencies": {"@floating-ui/utils": "^0.2.10"}, "deprecated": false, "description": "Positioning library for floating elements: tooltips, popovers, dropdowns, and more", "devDependencies": {"config": "0.0.0"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.core.d.mts", "default": "./dist/floating-ui.core.mjs"}, "types": "./dist/floating-ui.core.d.ts", "module": "./dist/floating-ui.core.esm.js", "default": "./dist/floating-ui.core.umd.js"}}, "files": ["dist"], "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "license": "MIT", "main": "./dist/floating-ui.core.umd.js", "module": "./dist/floating-ui.core.esm.js", "name": "@floating-ui/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/floating-ui/floating-ui.git", "directory": "packages/core"}, "scripts": {"build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json", "clean": "<PERSON><PERSON><PERSON> dist out-tsc", "dev": "rollup -c -w", "format": "prettier --write .", "lint": "eslint .", "publint": "publint", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc -b"}, "sideEffects": false, "types": "./dist/floating-ui.core.d.ts", "unpkg": "./dist/floating-ui.core.umd.min.js", "version": "1.7.2"}