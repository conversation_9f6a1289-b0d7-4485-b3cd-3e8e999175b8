{"_from": "pkg-types@^2.0.1", "_id": "pkg-types@2.2.0", "_inBundle": false, "_integrity": "sha512-2SM/GZGAEkPp3KWORxQZns4M+WSeXbC2HEvmOIJe3Cmiv6ieAJvdVhDldtHqM5J1Y7MrR1XhkBT/rMlhh9FdqQ==", "_location": "/unimport/local-pkg/pkg-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pkg-types@^2.0.1", "name": "pkg-types", "escapedName": "pkg-types", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/unimport/local-pkg"], "_resolved": "https://registry.npmjs.org/pkg-types/-/pkg-types-2.2.0.tgz", "_shasum": "049bf404f82a66c465200149457acf0c5fb0fb2d", "_spec": "pkg-types@^2.0.1", "_where": "/mnt/e/www/demo1/node_modules/unimport/node_modules/local-pkg", "bugs": {"url": "https://github.com/unjs/pkg-types/issues"}, "bundleDependencies": false, "dependencies": {"confbox": "^0.2.2", "exsolve": "^1.0.7", "pathe": "^2.0.3"}, "deprecated": false, "description": "Node.js utilities and TypeScript definitions for `package.json` and `tsconfig.json`", "devDependencies": {"@types/node": "^24.0.7", "@vitest/coverage-v8": "^3.2.4", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.30.0", "eslint-config-unjs": "^0.5.0", "expect-type": "^1.2.1", "jiti": "^2.4.2", "prettier": "^3.6.2", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.2.4"}, "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "files": ["dist"], "homepage": "https://github.com/unjs/pkg-types#readme", "license": "MIT", "name": "pkg-types", "packageManager": "pnpm@10.12.4", "repository": {"type": "git", "url": "git+https://github.com/unjs/pkg-types.git"}, "scripts": {"build": "unbuild", "dev": "vitest --typecheck", "lint": "eslint && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "vitest run --typecheck --coverage"}, "sideEffects": false, "types": "./dist/index.d.mts", "version": "2.2.0"}