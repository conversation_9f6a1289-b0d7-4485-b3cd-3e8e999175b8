{"_from": "pathe@^2.0.1", "_id": "pathe@2.0.3", "_inBundle": false, "_integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "_location": "/pathe", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pathe@^2.0.1", "name": "pathe", "escapedName": "pathe", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/mlly", "/pkg-types", "/unimport", "/unimport/local-pkg/pkg-types"], "_resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "_shasum": "3ecbec55421685b70a9da872b2cff3e1cbed1716", "_spec": "pathe@^2.0.1", "_where": "/mnt/e/www/demo1/node_modules/mlly", "bugs": {"url": "https://github.com/unjs/pathe/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Universal filesystem path utils", "devDependencies": {"@types/node": "^22.13.1", "@vitest/coverage-v8": "^3.0.5", "changelogen": "^0.5.7", "esbuild": "^0.25.0", "eslint": "^9.20.1", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.0", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.5", "zeptomatch": "^2.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./utils": {"import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "require": {"types": "./dist/utils.d.cts", "default": "./dist/utils.cjs"}}}, "files": ["dist", "utils.d.ts"], "homepage": "https://github.com/unjs/pathe#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "pathe", "repository": {"type": "git", "url": "git+https://github.com/unjs/pathe.git"}, "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint . --fix && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage", "test:types": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "./dist/index.d.ts", "version": "2.0.3"}