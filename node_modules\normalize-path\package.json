{"_from": "normalize-path@~3.0.0", "_id": "normalize-path@3.0.0", "_inBundle": false, "_integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "_location": "/normalize-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-path@~3.0.0", "name": "normalize-path", "escapedName": "normalize-path", "rawSpec": "~3.0.0", "saveSpec": null, "fetchSpec": "~3.0.0"}, "_requiredBy": ["/anymatch", "/chokidar"], "_resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "_shasum": "0dcd69ff23a1c9b11fd0978316644a0388216a65", "_spec": "normalize-path@~3.0.0", "_where": "/mnt/e/www/demo1/node_modules/chokidar", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "deprecated": false, "description": "Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.", "devDependencies": {"gulp-format-md": "^1.0.0", "minimist": "^1.2.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/normalize-path", "keywords": ["absolute", "backslash", "delimiter", "file", "file-path", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "relative", "separator", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "license": "MIT", "main": "index.js", "name": "normalize-path", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/normalize-path.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "Other useful path-related libraries:", "list": ["contains-path", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-ends-with", "unixify"]}, "lint": {"reflinks": true}}, "version": "3.0.0"}