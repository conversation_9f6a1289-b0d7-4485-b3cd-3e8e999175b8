{"_from": "@floating-ui/utils@^0.2.10", "_id": "@floating-ui/utils@0.2.10", "_inBundle": false, "_integrity": "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==", "_location": "/@floating-ui/utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@floating-ui/utils@^0.2.10", "name": "@floating-ui/utils", "escapedName": "@floating-ui%2futils", "scope": "@floating-ui", "rawSpec": "^0.2.10", "saveSpec": null, "fetchSpec": "^0.2.10"}, "_requiredBy": ["/@floating-ui/core", "/@floating-ui/dom"], "_resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz", "_shasum": "a2a1e3812d14525f725d011a73eceb41fef5bc1c", "_spec": "@floating-ui/utils@^0.2.10", "_where": "/mnt/e/www/demo1/node_modules/@floating-ui/dom", "author": {"name": "atomiks"}, "bugs": {"url": "https://github.com/floating-ui/floating-ui"}, "bundleDependencies": false, "deprecated": false, "description": "Utilities for Floating UI", "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "config": "0.0.0"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.utils.d.mts", "default": "./dist/floating-ui.utils.mjs"}, "types": "./dist/floating-ui.utils.d.ts", "module": "./dist/floating-ui.utils.esm.js", "default": "./dist/floating-ui.utils.umd.js"}, "./dom": {"import": {"types": "./dist/floating-ui.utils.dom.d.mts", "default": "./dist/floating-ui.utils.dom.mjs"}, "types": "./dist/floating-ui.utils.dom.d.ts", "module": "./dist/floating-ui.utils.dom.esm.js", "default": "./dist/floating-ui.utils.dom.umd.js"}}, "files": ["dist", "dom"], "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "license": "MIT", "main": "./dist/floating-ui.utils.umd.js", "module": "./dist/floating-ui.utils.esm.js", "name": "@floating-ui/utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/floating-ui/floating-ui.git", "directory": "packages/utils"}, "scripts": {"build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json --aec api-extractor.json --aec api-extractor.dom.json --aec api-extractor.react.json", "clean": "rim<PERSON>f dist out-tsc dom react", "dev": "rollup -c -w", "format": "prettier --write .", "lint": "eslint .", "publint": "publint", "test": "vitest run --globals", "test:watch": "vitest watch --globals", "typecheck": "tsc -b"}, "sideEffects": false, "types": "./dist/floating-ui.utils.d.ts", "version": "0.2.10"}