{"_from": "function-bind@^1.1.2", "_id": "function-bind@1.1.2", "_inBundle": false, "_integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "_location": "/function-bind", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "function-bind@^1.1.2", "name": "function-bind", "escapedName": "function-bind", "rawSpec": "^1.1.2", "saveSpec": null, "fetchSpec": "^1.1.2"}, "_requiredBy": ["/call-bind-apply-helpers", "/get-intrinsic", "/hasown"], "_resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "_shasum": "2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c", "_spec": "function-bind@^1.1.2", "_where": "/mnt/e/www/demo1/node_modules/get-intrinsic", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "deprecated": false, "description": "Implementation of Function.prototype.bind", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/Raynos/function-bind", "keywords": ["function", "bind", "shim", "es5"], "license": "MIT", "main": "index", "name": "function-bind", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/Raynos/function-bind.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.1.2"}