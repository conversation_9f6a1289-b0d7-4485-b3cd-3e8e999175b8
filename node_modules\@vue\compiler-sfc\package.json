{"_from": "@vue/compiler-sfc@3.5.17", "_id": "@vue/compiler-sfc@3.5.17", "_inBundle": false, "_integrity": "sha512-rQQxbRJMgTqwRugtjw0cnyQv9cP4/4BxWfTdRBkqsTfLOHWykLzbOc3C4GGzAmdMDxhzU/1Ija5bTjMVrddqww==", "_location": "/@vue/compiler-sfc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-sfc@3.5.17", "name": "@vue/compiler-sfc", "escapedName": "@vue%2fcompiler-sfc", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz", "_shasum": "c518871276e26593612bdab36f3f5bcd053b13bf", "_spec": "@vue/compiler-sfc@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.27.5", "@vue/compiler-core": "3.5.17", "@vue/compiler-dom": "3.5.17", "@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-sfc", "devDependencies": {"@babel/types": "^7.27.6", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "~10.0.3", "postcss-modules": "^6.0.1", "postcss-selector-parser": "^7.1.0", "pug": "^3.0.3", "sass": "^1.89.2"}, "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "name": "@vue/compiler-sfc", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "types": "dist/compiler-sfc.d.ts", "version": "3.5.17"}