<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复工具 - 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-vial text-primary"></i>
                    数据修复工具 - 功能测试
                </h1>
            </div>
        </div>

        <!-- API测试 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-code"></i> API接口测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="apiSelect" class="form-label">选择API接口</label>
                            <select class="form-select" id="apiSelect">
                                <option value="api/user_repair.php">用户修复接口</option>
                                <option value="api/order_repair.php">订单修复接口</option>
                                <option value="api/product_repair.php">商品修复接口</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="testData" class="form-label">测试数据 (JSON)</label>
                            <textarea class="form-control" id="testData" rows="8">{
  "table_name": "users",
  "action": "update",
  "query_field": "user_id",
  "user_id": "1",
  "username": "test_user",
  "email": "<EMAIL>",
  "status": "active"
}</textarea>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="testAPI()">
                            <i class="fas fa-play"></i> 测试API
                        </button>
                        
                        <div id="apiResult" class="mt-3" style="display: none;">
                            <h6>响应结果:</h6>
                            <pre class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> 数据库连接测试</h5>
                    </div>
                    <div class="card-body">
                        <p>测试数据库连接和基本查询功能</p>
                        
                        <button type="button" class="btn btn-success" onclick="testDatabase()">
                            <i class="fas fa-database"></i> 测试数据库连接
                        </button>
                        
                        <div id="dbResult" class="mt-3" style="display: none;">
                            <h6>测试结果:</h6>
                            <div class="alert" role="alert"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV测试 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-csv"></i> CSV解析测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="csvTestData" class="form-label">CSV测试数据</label>
                                    <textarea class="form-control" id="csvTestData" rows="6">user_id,username,email,status
1,user1,<EMAIL>,active
2,user2,<EMAIL>,inactive
3,user3,<EMAIL>,active</textarea>
                                </div>
                                
                                <button type="button" class="btn btn-info" onclick="testCSVParsing()">
                                    <i class="fas fa-cogs"></i> 测试CSV解析
                                </button>
                            </div>
                            
                            <div class="col-md-6">
                                <div id="csvResult" style="display: none;">
                                    <h6>解析结果:</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered" id="csvResultTable">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能测试 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tachometer-alt"></i> 性能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="concurrency" class="form-label">并发数</label>
                                <input type="number" class="form-control" id="concurrency" value="10" min="1" max="100">
                            </div>
                            <div class="col-md-4">
                                <label for="requestCount" class="form-label">请求数量</label>
                                <input type="number" class="form-control" id="requestCount" value="50" min="1" max="1000">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="button" class="btn btn-warning" onclick="performanceTest()">
                                    <i class="fas fa-rocket"></i> 开始性能测试
                                </button>
                            </div>
                        </div>
                        
                        <div id="performanceResult" class="mt-3" style="display: none;">
                            <h6>性能测试结果:</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4 id="totalTime">0</h4>
                                            <small>总耗时(秒)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4 id="successRate">0%</h4>
                                            <small>成功率</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4 id="avgResponse">0</h4>
                                            <small>平均响应时间(ms)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4 id="throughput">0</h4>
                                            <small>吞吐量(req/s)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回主页 -->
        <div class="row mt-4 mb-5">
            <div class="col-12 text-center">
                <a href="index.html" class="btn btn-lg btn-primary">
                    <i class="fas fa-home"></i> 返回主页
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // API测试
        async function testAPI() {
            const apiUrl = document.getElementById('apiSelect').value;
            const testData = document.getElementById('testData').value;
            const resultDiv = document.getElementById('apiResult');
            const resultPre = resultDiv.querySelector('pre');
            
            try {
                const data = JSON.parse(testData);
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                resultPre.textContent = JSON.stringify(result, null, 2);
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultPre.textContent = '错误: ' + error.message;
                resultDiv.style.display = 'block';
            }
        }

        // 数据库连接测试
        async function testDatabase() {
            const resultDiv = document.getElementById('dbResult');
            const alertDiv = resultDiv.querySelector('.alert');
            
            try {
                const response = await fetch('api/user_repair.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table_name: 'users',
                        action: 'update',
                        query_field: 'user_id',
                        user_id: '999999'  // 不存在的ID，用于测试连接
                    })
                });
                
                if (response.ok) {
                    alertDiv.className = 'alert alert-success';
                    alertDiv.textContent = '数据库连接正常！';
                } else {
                    alertDiv.className = 'alert alert-danger';
                    alertDiv.textContent = '数据库连接失败！';
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = '连接错误: ' + error.message;
                resultDiv.style.display = 'block';
            }
        }

        // CSV解析测试
        function testCSVParsing() {
            const csvData = document.getElementById('csvTestData').value;
            const resultDiv = document.getElementById('csvResult');
            const table = document.getElementById('csvResultTable');
            
            try {
                const parsed = parseCSV(csvData);
                
                table.innerHTML = '';
                parsed.forEach((row, index) => {
                    const tr = document.createElement('tr');
                    if (index === 0) {
                        row.forEach(cell => {
                            const th = document.createElement('th');
                            th.textContent = cell;
                            tr.appendChild(th);
                        });
                    } else {
                        row.forEach(cell => {
                            const td = document.createElement('td');
                            td.textContent = cell;
                            tr.appendChild(td);
                        });
                    }
                    table.appendChild(tr);
                });
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                alert('CSV解析错误: ' + error.message);
            }
        }

        // CSV解析函数
        function parseCSV(csv) {
            const lines = csv.split('\n');
            const result = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line) {
                    const row = line.split(',').map(cell => cell.trim());
                    result.push(row);
                }
            }
            
            return result;
        }

        // 性能测试
        async function performanceTest() {
            const concurrency = parseInt(document.getElementById('concurrency').value);
            const requestCount = parseInt(document.getElementById('requestCount').value);
            const resultDiv = document.getElementById('performanceResult');
            
            const startTime = Date.now();
            let successCount = 0;
            let totalResponseTime = 0;
            
            // 分组并发请求
            const chunks = Math.ceil(requestCount / concurrency);
            
            for (let i = 0; i < chunks; i++) {
                const promises = [];
                const currentBatchSize = Math.min(concurrency, requestCount - i * concurrency);
                
                for (let j = 0; j < currentBatchSize; j++) {
                    promises.push(performSingleRequest());
                }
                
                const results = await Promise.all(promises);
                results.forEach(result => {
                    if (result.success) successCount++;
                    totalResponseTime += result.responseTime;
                });
            }
            
            const endTime = Date.now();
            const totalTime = (endTime - startTime) / 1000;
            const successRate = (successCount / requestCount * 100).toFixed(1);
            const avgResponseTime = (totalResponseTime / requestCount).toFixed(2);
            const throughput = (requestCount / totalTime).toFixed(2);
            
            document.getElementById('totalTime').textContent = totalTime.toFixed(2);
            document.getElementById('successRate').textContent = successRate + '%';
            document.getElementById('avgResponse').textContent = avgResponseTime;
            document.getElementById('throughput').textContent = throughput;
            
            resultDiv.style.display = 'block';
        }

        async function performSingleRequest() {
            const requestStart = Date.now();
            
            try {
                const response = await fetch('api/user_repair.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table_name: 'users',
                        action: 'update',
                        query_field: 'user_id',
                        user_id: '1',
                        username: 'test_' + Date.now()
                    })
                });
                
                const requestEnd = Date.now();
                return {
                    success: response.ok,
                    responseTime: requestEnd - requestStart
                };
                
            } catch (error) {
                const requestEnd = Date.now();
                return {
                    success: false,
                    responseTime: requestEnd - requestStart
                };
            }
        }
    </script>
</body>
</html>
