{"_from": "@element-plus/icons-vue@^2.3.0", "_id": "@element-plus/icons-vue@2.3.1", "_inBundle": false, "_integrity": "sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==", "_location": "/@element-plus/icons-vue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@element-plus/icons-vue@^2.3.0", "name": "@element-plus/icons-vue", "escapedName": "@element-plus%2ficons-vue", "scope": "@element-plus", "rawSpec": "^2.3.0", "saveSpec": null, "fetchSpec": "^2.3.0"}, "_requiredBy": ["/", "/element-plus"], "_resolved": "https://registry.npmjs.org/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz", "_shasum": "1f635ad5fdd5c85ed936481525570e82b5a8307a", "_spec": "@element-plus/icons-vue@^2.3.0", "_where": "/mnt/e/www/demo1", "bugs": {"url": "https://github.com/element-plus/element-plus-icons/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Vue components of Element Plus Icons collection.", "devDependencies": {"@element-plus/icons-svg": "2.3.1", "@pnpm/find-workspace-dir": "^6.0.2", "@pnpm/find-workspace-packages": "^6.0.9", "@pnpm/logger": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/node": "^20.10.0", "@types/prettier": "^3.0.0", "camelcase": "^8.0.0", "chalk": "^5.3.0", "consola": "^3.2.3", "esbuild": "^0.19.8", "esbuild-plugin-globals": "^0.2.0", "fast-glob": "^3.3.2", "fs-extra": "^11.1.1", "npm-run-all": "^4.1.5", "prettier": "^3.1.0", "tsx": "^4.5.0", "typescript": "^5.3.2", "unplugin-vue": "^4.5.0", "vue": "^3.3.9", "vue-tsc": "^1.8.22"}, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "./global": {"types": "./dist/types/global.d.ts", "require": "./dist/global.cjs", "import": "./dist/global.js"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://element-plus.org/", "jsdelivr": "dist/index.iife.min.js", "keywords": ["icon", "svg", "vue", "element-plus"], "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.js", "name": "@element-plus/icons-vue", "peerDependencies": {"vue": "^3.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/element-plus/element-plus-icons.git", "directory": "packages/vue"}, "scripts": {"build": "pnpm run build:generate && run-p build:build build:types", "build:build": "NODE_ENV=production tsx build/build.ts", "build:generate": "tsx build/generate.ts", "build:types": "vue-tsc --declaration --emitDeclarationOnly"}, "sideEffects": false, "type": "module", "types": "./dist/types/index.d.ts", "typesVersions": {"*": {"*": ["./*", "./dist/types/*"]}}, "unpkg": "dist/index.iife.min.js", "version": "2.3.1"}