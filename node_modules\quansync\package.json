{"_from": "quansync@^0.2.8", "_id": "quansync@0.2.10", "_inBundle": false, "_integrity": "sha512-t41VRkMYbkHyCYmOvx/6URnN80H7k4X0lLdBMGsz+maAwrJQYB1djpV6vHrQIBE0WBSGqhtEHrK9U3DWWH8v7A==", "_location": "/quansync", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "quansync@^0.2.8", "name": "quansync", "escapedName": "quansync", "rawSpec": "^0.2.8", "saveSpec": null, "fetchSpec": "^0.2.8"}, "_requiredBy": ["/unimport/local-pkg"], "_resolved": "https://registry.npmjs.org/quansync/-/quansync-0.2.10.tgz", "_shasum": "32053cf166fa36511aae95fc49796116f2dc20e1", "_spec": "quansync@^0.2.8", "_where": "/mnt/e/www/demo1/node_modules/unimport/node_modules/local-pkg", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/quansync-dev/quansync/issues"}, "bundleDependencies": false, "contributors": [{"name": "三咲智子 <PERSON>g", "email": "<EMAIL>"}], "deprecated": false, "description": "Create sync/async APIs with usable logic", "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@types/node": "^22.13.10", "bumpp": "^10.1.0", "eslint": "^9.22.0", "gensync": "1.0.0-beta.2", "lint-staged": "^15.5.0", "mitata": "^1.0.34", "simple-git-hooks": "^2.11.1", "tsx": "^4.19.3", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vite": "^6.2.2", "vitest": "^3.0.9"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./macro": {"import": "./dist/macro.mjs", "require": "./dist/macro.cjs"}, "./types": {"import": "./dist/types.mjs", "require": "./dist/types.cjs"}}, "files": ["dist"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/antfu"}, {"type": "individual", "url": "https://github.com/sponsors/sxzz"}], "homepage": "https://github.com/quansync-dev/quansync#readme", "keywords": ["async", "sync", "generator"], "license": "MIT", "lint-staged": {"*": "eslint --fix"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "name": "quansync", "repository": {"type": "git", "url": "git+https://github.com/quansync-dev/quansync.git"}, "scripts": {"benchmark": "node scripts/benchmark.js", "build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "start": "tsx src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}, "sideEffects": false, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "type": "module", "types": "./dist/index.d.mts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "version": "0.2.10"}