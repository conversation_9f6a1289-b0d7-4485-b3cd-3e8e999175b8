<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能演示 - 数据修复工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-magic text-primary"></i>
                    功能演示页面
                </h1>
                <div class="text-center mb-4">
                    <a href="index.html" class="btn btn-primary me-2">
                        <i class="fas fa-home"></i> 返回主页
                    </a>
                    <a href="test.html" class="btn btn-secondary">
                        <i class="fas fa-vial"></i> 测试页面
                    </a>
                </div>
            </div>
        </div>

        <!-- Excel粘贴演示 -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-excel text-success"></i> Excel数据粘贴演示</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>示例Excel数据（复制下面的内容）：</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre style="margin: 0; font-size: 12px;">user_id	username	email	status
101	demo_user1	<EMAIL>	active
102	demo_user2	<EMAIL>	inactive
103	demo_user3	<EMAIL>	active
104	demo_user4	<EMAIL>	banned</pre>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    选中上面的文本，复制后粘贴到右侧表格中
                                </small>
                            </div>
                            <div class="col-md-6">
                                <h6>粘贴到这个表格：</h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-mouse-pointer"></i>
                                    点击表格任意位置，然后按 Ctrl+V 粘贴
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered editable-table" id="demoTable">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="text" value="列1" class="form-control form-control-sm">
                                                </th>
                                                <th>
                                                    <input type="text" value="列2" class="form-control form-control-sm">
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control form-control-sm" placeholder="输入数据"></td>
                                                <td><input type="text" class="form-control form-control-sm" placeholder="输入数据"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 样式演示 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-palette"></i> 新样式演示</h5>
                    </div>
                    <div class="card-body">
                        <h6>表头样式：</h6>
                        <div class="table-responsive mb-3">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>新表头样式</th>
                                        <th>渐变背景</th>
                                        <th>白色文字</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>数据1</td>
                                        <td>数据2</td>
                                        <td>数据3</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <h6>按钮样式：</h6>
                        <div class="btn-group mb-3" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加行
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加列
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-csv"></i> CSV上传演示</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="demoCSV" class="form-label">选择CSV文件</label>
                            <input type="file" class="form-control" id="demoCSV" accept=".csv" onchange="handleDemoCSV(event)">
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <label for="demoThreads" class="form-label me-2">线程数:</label>
                            <input type="number" class="form-control me-3" id="demoThreads" value="50" min="1" max="200" style="width: 100px;">
                            <button type="button" class="btn btn-primary" id="demoStartBtn" disabled>
                                <i class="fas fa-play"></i> 开始修复
                            </button>
                        </div>
                        
                        <div id="demoProgress" style="display: none;">
                            <div class="progress mb-2">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="demoProgressBar">0%</div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">演示进度条效果</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                可以使用 examples/ 目录下的示例CSV文件进行测试
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-question-circle"></i> 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-clipboard text-primary"></i> Excel粘贴功能</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> 支持从Excel直接复制粘贴</li>
                                    <li><i class="fas fa-check text-success"></i> 自动调整表格行列数</li>
                                    <li><i class="fas fa-check text-success"></i> 保持数据格式</li>
                                    <li><i class="fas fa-check text-success"></i> 支持制表符分隔数据</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-paint-brush text-info"></i> 界面改进</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> 新的表头渐变样式</li>
                                    <li><i class="fas fa-check text-success"></i> 更突出的按钮设计</li>
                                    <li><i class="fas fa-check text-success"></i> 改进的视觉效果</li>
                                    <li><i class="fas fa-check text-success"></i> 更好的用户体验</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-bug text-warning"></i> 问题修复</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> 修复CSV上传按钮问题</li>
                                    <li><i class="fas fa-check text-success"></i> 改进文件处理逻辑</li>
                                    <li><i class="fas fa-check text-success"></i> 优化错误处理</li>
                                    <li><i class="fas fa-check text-success"></i> 提升稳定性</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加粘贴功能到演示表格
        document.addEventListener('DOMContentLoaded', function() {
            const demoTable = document.getElementById('demoTable');
            addPasteEventListener(demoTable);
        });

        // 简化的粘贴事件监听
        function addPasteEventListener(table) {
            table.addEventListener('paste', function(e) {
                e.preventDefault();
                
                let pastedData = '';
                if (e.clipboardData) {
                    pastedData = e.clipboardData.getData('text');
                }
                
                if (!pastedData.trim()) return;
                
                // 解析粘贴的数据
                const rows = pastedData.trim().split('\n');
                const parsedData = rows.map(row => {
                    return row.split('\t').map(cell => cell.trim());
                });
                
                if (parsedData.length === 0) return;
                
                // 确定需要的列数
                const maxColumns = Math.max(...parsedData.map(row => row.length));
                const currentColumns = table.querySelector('thead tr').children.length;
                
                // 如果需要更多列，自动添加
                if (maxColumns > currentColumns) {
                    const headerRow = table.querySelector('thead tr');
                    for (let i = currentColumns; i < maxColumns; i++) {
                        const th = document.createElement('th');
                        th.innerHTML = `<input type="text" value="列${i + 1}" class="form-control form-control-sm">`;
                        headerRow.appendChild(th);
                    }
                }
                
                // 清空现有数据行
                const tbody = table.querySelector('tbody');
                tbody.innerHTML = '';
                
                // 添加粘贴的数据
                parsedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    
                    for (let i = 0; i < maxColumns; i++) {
                        const td = document.createElement('td');
                        const value = rowData[i] || '';
                        td.innerHTML = `<input type="text" class="form-control form-control-sm" value="${escapeHtml(value)}" placeholder="输入数据">`;
                        row.appendChild(td);
                    }
                    
                    tbody.appendChild(row);
                });
                
                // 显示成功消息
                showMessage(`成功粘贴 ${parsedData.length} 行 ${maxColumns} 列数据`, 'success');
            });
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 演示CSV处理
        function handleDemoCSV(event) {
            const file = event.target.files[0];
            const startBtn = document.getElementById('demoStartBtn');
            
            if (file) {
                startBtn.disabled = false;
                showMessage('CSV文件已选择，可以开始处理', 'info');
                
                // 演示进度条
                startBtn.onclick = function() {
                    const progressDiv = document.getElementById('demoProgress');
                    const progressBar = document.getElementById('demoProgressBar');
                    
                    progressDiv.style.display = 'block';
                    let progress = 0;
                    
                    const interval = setInterval(() => {
                        progress += 10;
                        progressBar.style.width = progress + '%';
                        progressBar.textContent = progress + '%';
                        
                        if (progress >= 100) {
                            clearInterval(interval);
                            showMessage('演示完成！', 'success');
                        }
                    }, 200);
                };
            } else {
                startBtn.disabled = true;
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
