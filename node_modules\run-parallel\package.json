{"_from": "run-parallel@^1.1.9", "_id": "run-parallel@1.2.0", "_inBundle": false, "_integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "_location": "/run-parallel", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "run-parallel@^1.1.9", "name": "run-parallel", "escapedName": "run-parallel", "rawSpec": "^1.1.9", "saveSpec": null, "fetchSpec": "^1.1.9"}, "_requiredBy": ["/@nodelib/fs.scandir"], "_resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "_shasum": "66d1368da7bdf921eb9d95bd1a9229e7f21a43ee", "_spec": "run-parallel@^1.1.9", "_where": "/mnt/e/www/demo1/node_modules/@nodelib/fs.scandir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/run-parallel/issues"}, "bundleDependencies": false, "dependencies": {"queue-microtask": "^1.2.2"}, "deprecated": false, "description": "Run an array of functions in parallel", "devDependencies": {"airtap": "^3.0.0", "standard": "*", "tape": "^5.0.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/feross/run-parallel", "keywords": ["parallel", "async", "function", "callback", "asynchronous", "run", "array", "run parallel"], "license": "MIT", "main": "index.js", "name": "run-parallel", "repository": {"type": "git", "url": "git://github.com/feross/run-parallel.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "version": "1.2.0"}