<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入通用数据库类
require_once 'user_repair.php';

// 订单数据修复类
class OrderRepair {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function processRequest($data) {
        try {
            // 验证必要参数
            if (!isset($data['table_name']) || !isset($data['action']) || !isset($data['query_field'])) {
                throw new Exception("缺少必要参数");
            }
            
            $tableName = $this->sanitizeTableName($data['table_name']);
            $action = $data['action'];
            $queryField = $data['query_field'];
            
            // 订单特殊验证
            $this->validateOrderData($data, $action);
            
            // 根据动作执行不同操作
            switch ($action) {
                case 'update':
                    return $this->updateOrder($tableName, $queryField, $data);
                case 'insert':
                    return $this->insertOrder($tableName, $data);
                case 'delete':
                    return $this->deleteOrder($tableName, $queryField, $data);
                default:
                    throw new Exception("不支持的操作类型: " . $action);
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function validateOrderData($data, $action) {
        // 订单金额验证
        if (isset($data['amount']) && !is_numeric($data['amount'])) {
            throw new Exception("订单金额必须是数字");
        }
        
        // 订单状态验证
        if (isset($data['status'])) {
            $validStatuses = ['pending', 'paid', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new Exception("无效的订单状态");
            }
        }
        
        // 用户ID验证
        if (isset($data['user_id']) && !is_numeric($data['user_id'])) {
            throw new Exception("用户ID必须是数字");
        }
    }
    
    private function updateOrder($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        // 先检查订单是否存在
        $checkSql = "SELECT COUNT(*) FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute(['query_value' => $data[$queryField]]);
        
        if ($checkStmt->fetchColumn() == 0) {
            throw new Exception("订单不存在");
        }
        
        // 构建更新字段
        $updateFields = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field']) && $key !== $queryField) {
                $updateFields[] = "`{$key}` = :{$key}";
                $params[$key] = $value;
            }
        }
        
        if (empty($updateFields)) {
            throw new Exception("没有要更新的字段");
        }
        
        // 添加更新时间
        $updateFields[] = "`updated_at` = NOW()";
        
        $sql = "UPDATE `{$tableName}` SET " . implode(', ', $updateFields) . " WHERE `{$queryField}` = :query_value";
        $params['query_value'] = $data[$queryField];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        if ($affectedRows > 0) {
            return [
                'success' => true,
                'message' => "成功更新订单",
                'affected_rows' => $affectedRows
            ];
        } else {
            return [
                'success' => false,
                'message' => "订单数据未发生变化"
            ];
        }
    }
    
    private function insertOrder($tableName, $data) {
        $conn = $this->db->getConnection();
        
        // 检查必要字段
        $requiredFields = ['user_id', 'amount'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("缺少必要字段: {$field}");
            }
        }
        
        // 构建插入字段
        $fields = [];
        $placeholders = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            if (!in_array($key, ['table_name', 'action', 'query_field'])) {
                $fields[] = "`{$key}`";
                $placeholders[] = ":{$key}";
                $params[$key] = $value;
            }
        }
        
        // 添加创建时间
        $fields[] = "`created_at`";
        $placeholders[] = "NOW()";
        
        $sql = "INSERT INTO `{$tableName}` (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        return [
            'success' => true,
            'message' => "成功创建订单",
            'insert_id' => $conn->lastInsertId()
        ];
    }
    
    private function deleteOrder($tableName, $queryField, $data) {
        $conn = $this->db->getConnection();
        
        if (!isset($data[$queryField])) {
            throw new Exception("缺少查询字段值");
        }
        
        // 检查订单状态，某些状态的订单不能删除
        $checkSql = "SELECT status FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute(['query_value' => $data[$queryField]]);
        $order = $checkStmt->fetch();
        
        if (!$order) {
            throw new Exception("订单不存在");
        }
        
        if (in_array($order['status'], ['paid', 'shipped', 'delivered'])) {
            throw new Exception("该状态的订单不能删除");
        }
        
        $sql = "DELETE FROM `{$tableName}` WHERE `{$queryField}` = :query_value";
        $params = ['query_value' => $data[$queryField]];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        return [
            'success' => true,
            'message' => "成功删除订单",
            'affected_rows' => $affectedRows
        ];
    }
    
    private function sanitizeTableName($tableName) {
        // 只允许字母、数字和下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        return $tableName;
    }
}

// 主处理逻辑
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("只支持POST请求");
    }
    
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("无效的JSON数据");
    }
    
    $orderRepair = new OrderRepair();
    $result = $orderRepair->processRequest($data);
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
