{"_from": "mlly@^1.7.3", "_id": "mlly@1.7.4", "_inBundle": false, "_integrity": "sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==", "_location": "/mlly", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mlly@^1.7.3", "name": "mlly", "escapedName": "mlly", "rawSpec": "^1.7.3", "saveSpec": null, "fetchSpec": "^1.7.3"}, "_requiredBy": ["/local-pkg", "/pkg-types", "/unimport", "/unimport/local-pkg"], "_resolved": "https://registry.npmjs.org/mlly/-/mlly-1.7.4.tgz", "_shasum": "3d7295ea2358ec7a271eaa5d000a0f84febe100f", "_spec": "mlly@^1.7.3", "_where": "/mnt/e/www/demo1/node_modules/local-pkg", "bugs": {"url": "https://github.com/unjs/mlly/issues"}, "bundleDependencies": false, "dependencies": {"acorn": "^8.14.0", "pathe": "^2.0.1", "pkg-types": "^1.3.0", "ufo": "^1.5.4"}, "deprecated": false, "description": "Missing ECMAScript module utils for Node.js", "devDependencies": {"@types/node": "^22.10.5", "@vitest/coverage-v8": "^2.1.8", "changelogen": "^0.5.7", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "import-meta-resolve": "^4.1.0", "jiti": "^2.4.2", "prettier": "^3.4.2", "std-env": "^3.8.0", "typescript": "^5.7.2", "unbuild": "^3.2.0", "vitest": "^2.1.8"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "files": ["dist"], "homepage": "https://github.com/unjs/mlly#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "mlly", "packageManager": "pnpm@9.15.3", "repository": {"type": "git", "url": "git+https://github.com/unjs/mlly.git"}, "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint src test && prettier -c src test", "lint:fix": "eslint src test --fix && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run", "test:types": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "./dist/index.d.ts", "version": "1.7.4"}