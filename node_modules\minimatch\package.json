{"_from": "minimatch@^9.0.3", "_id": "minimatch@9.0.5", "_inBundle": false, "_integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "_location": "/minimatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minimatch@^9.0.3", "name": "minimatch", "escapedName": "minimatch", "rawSpec": "^9.0.3", "saveSpec": null, "fetchSpec": "^9.0.3"}, "_requiredBy": ["/unplugin-auto-import", "/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "_shasum": "d74f9dd6b57d83d8e98cfb82133b03978bc929e5", "_spec": "minimatch@^9.0.3", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "bundleDependencies": false, "dependencies": {"brace-expansion": "^2.0.1"}, "deprecated": false, "description": "a glob matcher in javascript", "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.15.11", "@types/tap": "^15.0.8", "eslint-config-prettier": "^8.6.0", "mkdirp": "1", "prettier": "^2.8.2", "tap": "^18.7.2", "ts-node": "^10.9.1", "tshy": "^1.12.0", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/minimatch#readme", "license": "ISC", "main": "./dist/commonjs/index.js", "name": "minimatch", "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "scripts": {"benchmark": "node benchmark/index.js", "format": "prettier --write . --loglevel warn", "postversion": "npm publish", "prepare": "tshy", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "snap": "tap", "test": "tap", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "9.0.5"}