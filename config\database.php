<?php
/**
 * 数据库配置文件
 * 请根据您的实际环境修改以下配置
 */

// 数据库配置类
class DatabaseConfig {
    // 数据库主机地址
    const HOST = 'localhost';
    
    // 数据库用户名
    const USERNAME = 'root';
    
    // 数据库密码
    const PASSWORD = '';
    
    // 数据库名称
    const DATABASE = 'repair_tool';
    
    // 数据库端口
    const PORT = 3306;
    
    // 字符集
    const CHARSET = 'utf8mb4';
    
    // 连接选项
    public static function getPDOOptions() {
        return [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . self::CHARSET
        ];
    }
    
    // 获取DSN字符串
    public static function getDSN() {
        return sprintf(
            "mysql:host=%s;port=%d;dbname=%s;charset=%s",
            self::HOST,
            self::PORT,
            self::DATABASE,
            self::CHARSET
        );
    }
}

// 数据库连接单例类
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $this->connection = new PDO(
                DatabaseConfig::getDSN(),
                DatabaseConfig::USERNAME,
                DatabaseConfig::PASSWORD,
                DatabaseConfig::getPDOOptions()
            );
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // 防止克隆
    private function __clone() {}
    
    // 防止反序列化
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// 应用配置类
class AppConfig {
    // 应用名称
    const APP_NAME = '数据修复工具';
    
    // 版本号
    const VERSION = '1.0.0';
    
    // 调试模式
    const DEBUG = true;
    
    // 日志级别
    const LOG_LEVEL = 'INFO';
    
    // 最大并发线程数
    const MAX_THREADS = 200;
    
    // 请求超时时间（秒）
    const REQUEST_TIMEOUT = 30;
    
    // 允许操作的表名列表
    const ALLOWED_TABLES = [
        'users',
        'orders',
        'products'
    ];
    
    // 是否启用操作日志
    const ENABLE_LOGGING = true;
    
    // 日志文件路径
    const LOG_FILE = '../logs/repair.log';
    
    // 获取所有配置
    public static function getAll() {
        return [
            'app_name' => self::APP_NAME,
            'version' => self::VERSION,
            'debug' => self::DEBUG,
            'log_level' => self::LOG_LEVEL,
            'max_threads' => self::MAX_THREADS,
            'request_timeout' => self::REQUEST_TIMEOUT,
            'allowed_tables' => self::ALLOWED_TABLES,
            'enable_logging' => self::ENABLE_LOGGING,
            'log_file' => self::LOG_FILE
        ];
    }
}

// 环境检测
class Environment {
    public static function check() {
        $errors = [];
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            $errors[] = 'PHP版本必须 >= 7.4.0，当前版本: ' . PHP_VERSION;
        }
        
        // 检查PDO扩展
        if (!extension_loaded('pdo')) {
            $errors[] = '缺少PDO扩展';
        }
        
        // 检查PDO MySQL驱动
        if (!extension_loaded('pdo_mysql')) {
            $errors[] = '缺少PDO MySQL驱动';
        }
        
        // 检查JSON扩展
        if (!extension_loaded('json')) {
            $errors[] = '缺少JSON扩展';
        }
        
        // 检查日志目录
        $logDir = dirname(AppConfig::LOG_FILE);
        if (!is_dir($logDir)) {
            if (!mkdir($logDir, 0755, true)) {
                $errors[] = '无法创建日志目录: ' . $logDir;
            }
        } elseif (!is_writable($logDir)) {
            $errors[] = '日志目录不可写: ' . $logDir;
        }
        
        return $errors;
    }
}

// 简单的日志类
class Logger {
    private static $instance = null;
    private $logFile;
    
    private function __construct() {
        $this->logFile = AppConfig::LOG_FILE;
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function log($level, $message, $context = []) {
        if (!AppConfig::ENABLE_LOGGING) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = empty($context) ? '' : ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function debug($message, $context = []) {
        if (AppConfig::DEBUG) {
            $this->log('DEBUG', $message, $context);
        }
    }
}

// 初始化检查
$envErrors = Environment::check();
if (!empty($envErrors)) {
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '环境检查失败',
        'errors' => $envErrors
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>
