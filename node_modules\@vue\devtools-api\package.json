{"_from": "@vue/devtools-api@^6.6.3", "_id": "@vue/devtools-api@6.6.4", "_inBundle": false, "_integrity": "sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==", "_location": "/@vue/devtools-api", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vue/devtools-api@^6.6.3", "name": "@vue/devtools-api", "escapedName": "@vue%2fdevtools-api", "scope": "@vue", "rawSpec": "^6.6.3", "saveSpec": null, "fetchSpec": "^6.6.3"}, "_requiredBy": ["/pinia", "/vue-router"], "_resolved": "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "_shasum": "cbe97fe0162b365edc1dba80e173f90492535343", "_spec": "@vue/devtools-api@^6.6.3", "_where": "/mnt/e/www/demo1/node_modules/pinia", "author": {"name": "<PERSON>"}, "browser": "lib/esm/index.js", "bugs": {"url": "https://github.com/vuejs/vue-devtools/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Interact with the Vue devtools from the page", "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}, "files": ["lib/cjs", "lib/esm"], "homepage": "https://github.com/vuejs/vue-devtools#readme", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "name": "@vue/devtools-api", "publishConfig": {"access": "public"}, "repository": {"url": "git+https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "sideEffects": false, "types": "lib/esm/index.d.ts", "version": "6.6.4"}