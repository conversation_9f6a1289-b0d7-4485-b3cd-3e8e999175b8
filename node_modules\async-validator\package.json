{"_from": "async-validator@^4.2.5", "_id": "async-validator@4.2.5", "_inBundle": false, "_integrity": "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==", "_location": "/async-validator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "async-validator@^4.2.5", "name": "async-validator", "escapedName": "async-validator", "rawSpec": "^4.2.5", "saveSpec": null, "fetchSpec": "^4.2.5"}, "_requiredBy": ["/element-plus"], "_resolved": "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz", "_shasum": "c96ea3332a521699d0afaaceed510a54656c6339", "_spec": "async-validator@^4.2.5", "_where": "/mnt/e/www/demo1/node_modules/element-plus", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "validate form asynchronous", "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "7.x", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "ts-node": "^10.8.1", "typescript": "^4.3.2"}, "files": ["dist-*/", "bin/"], "homepage": "https://github.com/yiminghe/async-validator", "keywords": ["validator", "validate", "async"], "license": "MIT", "main": "dist-node/index.js", "module": "dist-web/index.js", "name": "async-validator", "pika": true, "repository": {"type": "git", "url": "git+ssh://**************/yiminghe/async-validator.git"}, "sideEffects": false, "types": "dist-types/index.d.ts", "version": "4.2.5"}