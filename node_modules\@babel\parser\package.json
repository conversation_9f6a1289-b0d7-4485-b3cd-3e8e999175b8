{"# dependencies": "This package doesn't actually have runtime dependencies. @babel/types is only needed for type definitions.", "_from": "@babel/parser@^7.27.5", "_id": "@babel/parser@7.28.0", "_inBundle": false, "_integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "_location": "/@babel/parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/parser@^7.27.5", "name": "@babel/parser", "escapedName": "@babel%2fparser", "scope": "@babel", "rawSpec": "^7.27.5", "saveSpec": null, "fetchSpec": "^7.27.5"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "_shasum": "979829fbab51a29e13901e5a80713dbcb840825e", "_spec": "@babel/parser@^7.27.5", "_where": "/mnt/e/www/demo1/node_modules/@vue/compiler-core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bin": {"parser": "bin/babel-parser.js"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.28.0"}, "deprecated": false, "description": "A JavaScript parser", "devDependencies": {"@babel/code-frame": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-fixtures": "^7.28.0", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}, "files": ["bin", "lib", "typings/babel-parser.d.ts", "index.cjs"], "homepage": "https://babel.dev/docs/en/next/babel-parser", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "type": "commonjs", "types": "./typings/babel-parser.d.ts", "version": "7.28.0"}