{"_from": "math-intrinsics@^1.1.0", "_id": "math-intrinsics@1.1.0", "_inBundle": false, "_integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "_location": "/math-intrinsics", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "math-intrinsics@^1.1.0", "name": "math-intrinsics", "escapedName": "math-intrinsics", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/get-intrinsic"], "_resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "_shasum": "a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9", "_spec": "math-intrinsics@^1.1.0", "_where": "/mnt/e/www/demo1/node_modules/get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/math-intrinsics/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ES Math-related intrinsics and helpers, robustly cached.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.5.0", "eslint": "^8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {"./abs": "./abs.js", "./floor": "./floor.js", "./isFinite": "./isFinite.js", "./isInteger": "./isInteger.js", "./isNaN": "./isNaN.js", "./isNegativeZero": "./isNegativeZero.js", "./max": "./max.js", "./min": "./min.js", "./mod": "./mod.js", "./pow": "./pow.js", "./sign": "./sign.js", "./round": "./round.js", "./constants/maxArrayLength": "./constants/maxArrayLength.js", "./constants/maxSafeInteger": "./constants/maxSafeInteger.js", "./constants/maxValue": "./constants/maxValue.js", "./package.json": "./package.json"}, "homepage": "https://github.com/es-shims/math-intrinsics#readme", "license": "MIT", "main": false, "name": "math-intrinsics", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/math-intrinsics.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.1.0"}