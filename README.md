# 数据修复工具

一个基于H5+JavaScript+Bootstrap前端和PHP后端的本地数据修复工具，支持快速修复和大批量修复功能。

## 功能特性

### 🎯 核心功能
- **多标签页支持**：不同类型数据（用户、订单、商品）独立修复
- **快速修复**：可编辑表格，支持直接粘贴、修改表头、增删行列
- **大批量修复**：CSV文件导入，JavaScript本地解析，无需上传服务器
- **并发处理**：可配置线程数，分组并发请求提高效率
- **实时反馈**：快速修复显示表格结果，大批量修复显示进度条

### 📊 进度反馈
- **快速修复**：实时在表格中显示每行处理结果
- **大批量修复**：
  - 百分比进度条
  - 已处理/总数统计
  - 实时处理时长（精确到0.01秒）
  - 智能预计剩余时长（自动格式化为时分秒）
  - 总耗时统计

### 🛠 表格操作
- 支持直接粘贴数据到表格
- 可编辑表头名称
- 动态添加/删除行列
- 一键清空表格
- 导出表格为CSV格式

## 项目结构

```
demo1/
├── index.html              # 主页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   └── app.js             # 核心JavaScript逻辑
├── api/
│   ├── user_repair.php    # 用户数据修复接口
│   ├── order_repair.php   # 订单数据修复接口
│   └── product_repair.php # 商品数据修复接口
├── database/
│   └── init.sql           # 数据库初始化脚本
└── README.md              # 项目说明文档
```

## 安装部署

### 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器（Apache/Nginx）
- 现代浏览器（支持ES6+）

### 部署步骤

1. **克隆项目到Web目录**
   ```bash
   git clone <repository-url> /var/www/html/demo1
   ```

2. **配置数据库**
   ```bash
   # 导入数据库结构和示例数据
   mysql -u root -p < database/init.sql
   ```

3. **修改数据库配置**
   编辑 `api/user_repair.php` 中的数据库配置：
   ```php
   class DatabaseConfig {
       const HOST = 'localhost';
       const USERNAME = 'your_username';
       const PASSWORD = 'your_password';
       const DATABASE = 'repair_tool';
   }
   ```

4. **设置文件权限**
   ```bash
   chmod 755 /var/www/html/demo1
   chmod 644 /var/www/html/demo1/*.html
   chmod 644 /var/www/html/demo1/css/*
   chmod 644 /var/www/html/demo1/js/*
   chmod 755 /var/www/html/demo1/api/*.php
   ```

5. **访问应用**
   在浏览器中打开：`http://localhost/demo1/`

## 使用说明

### 快速修复功能

1. **选择标签页**：选择要修复的数据类型（用户/订单/商品）
2. **配置参数**：设置表名、更新动作、查询字段
3. **编辑表格**：
   - 直接在表格中输入数据
   - 可以粘贴Excel/CSV数据
   - 修改表头名称以匹配数据库字段
   - 添加或删除行列
4. **设置线程数**：默认10个并发线程
5. **开始修复**：点击"开始修复"按钮
6. **查看结果**：在表格最后一列查看每行的处理结果

### 大批量修复功能

1. **选择标签页**：选择要修复的数据类型
2. **配置参数**：设置表名、更新动作、查询字段
3. **上传CSV文件**：选择本地CSV文件（不会上传到服务器）
4. **预览数据**：查看CSV文件前10行数据
5. **设置线程数**：默认50个并发线程
6. **开始修复**：点击"开始修复"按钮
7. **监控进度**：实时查看进度条和统计信息

### 支持的操作类型

- **更新（update）**：根据查询字段更新现有记录
- **插入（insert）**：创建新记录
- **删除（delete）**：根据查询字段删除记录

## API接口说明

### 请求格式
```json
{
    "table_name": "users",
    "action": "update",
    "query_field": "user_id",
    "user_id": "123",
    "username": "newname",
    "email": "<EMAIL>"
}
```

### 响应格式
```json
{
    "success": true,
    "message": "成功更新 1 条记录",
    "affected_rows": 1
}
```

## 数据库表结构

### 用户表 (users)
- `user_id`: 主键
- `username`: 用户名（唯一）
- `email`: 邮箱（唯一）
- `status`: 状态（active/inactive/banned）

### 订单表 (orders)
- `order_id`: 主键
- `user_id`: 用户ID
- `amount`: 订单金额
- `status`: 订单状态
- `order_number`: 订单号（唯一）

### 商品表 (products)
- `product_id`: 主键
- `name`: 商品名称
- `sku`: 商品编码（唯一）
- `price`: 价格
- `stock`: 库存

## 安全特性

- **SQL注入防护**：使用PDO预处理语句
- **表名验证**：只允许字母、数字、下划线
- **数据验证**：各类型数据都有相应的验证规则
- **错误处理**：完善的异常处理机制
- **CORS支持**：支持跨域请求

## 性能优化

- **并发处理**：支持多线程并发请求
- **分组处理**：大数据量分组处理，避免内存溢出
- **进度反馈**：实时显示处理进度，提升用户体验
- **本地解析**：CSV文件本地解析，减少服务器负载

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务是否启动
   - 验证用户权限

2. **接口请求失败**
   - 检查PHP错误日志
   - 确认Web服务器配置
   - 验证文件权限

3. **CSV解析错误**
   - 确认CSV文件格式正确
   - 检查文件编码（建议UTF-8）
   - 验证数据完整性

4. **并发处理异常**
   - 适当降低线程数
   - 检查数据库连接池配置
   - 监控服务器资源使用

## 开发说明

### 扩展新的数据类型

1. 在 `js/app.js` 中的 `tabConfigs` 添加新配置
2. 创建对应的PHP接口文件
3. 在HTML中添加新的标签页
4. 实现相应的数据验证逻辑

### 自定义样式

修改 `css/style.css` 文件，支持：
- 主题色彩调整
- 响应式布局优化
- 动画效果定制

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
