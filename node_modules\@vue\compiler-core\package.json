{"_from": "@vue/compiler-core@3.5.17", "_id": "@vue/compiler-core@3.5.17", "_inBundle": false, "_integrity": "sha512-Xe+AittLbAyV0pabcN7cP7/BenRBNcteM4aSDCtRvGw0d9OL+HG1u/XHLY/kt1q4fyMeZYXyIYrsHuPSiDPosA==", "_location": "/@vue/compiler-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-core@3.5.17", "name": "@vue/compiler-core", "escapedName": "@vue%2fcompiler-core", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/@vue/compiler-dom", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.17.tgz", "_shasum": "23d291bd01b863da3ef2e26e7db84d8e01a9b4c5", "_spec": "@vue/compiler-core@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/@vue/compiler-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.27.5", "@vue/shared": "3.5.17", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-core", "devDependencies": {"@babel/types": "^7.27.6"}, "exports": {".": {"types": "./dist/compiler-core.d.ts", "node": {"production": "./dist/compiler-core.cjs.prod.js", "development": "./dist/compiler-core.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-core.esm-bundler.js", "import": "./dist/compiler-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "name": "@vue/compiler-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "types": "dist/compiler-core.d.ts", "version": "3.5.17"}