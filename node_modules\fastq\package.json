{"_from": "fastq@^1.6.0", "_id": "fastq@1.19.1", "_inBundle": false, "_integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "_location": "/fastq", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fastq@^1.6.0", "name": "fastq", "escapedName": "fastq", "rawSpec": "^1.6.0", "saveSpec": null, "fetchSpec": "^1.6.0"}, "_requiredBy": ["/@nodelib/fs.walk"], "_resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "_shasum": "d50eaba803c8846a883c16492821ebcd2cda55f5", "_spec": "fastq@^1.6.0", "_where": "/mnt/e/www/demo1/node_modules/@nodelib/fs.walk", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mcollina/fastq/issues"}, "bundleDependencies": false, "dependencies": {"reusify": "^1.0.4"}, "deprecated": false, "description": "Fast, in memory work queue", "devDependencies": {"async": "^3.1.0", "neo-async": "^2.6.1", "nyc": "^17.0.0", "pre-commit": "^1.2.2", "snazzy": "^9.0.0", "standard": "^16.0.0", "tape": "^5.0.0", "typescript": "^5.0.4"}, "homepage": "https://github.com/mcollina/fastq#readme", "keywords": ["fast", "queue", "async", "worker"], "license": "ISC", "main": "queue.js", "name": "fastq", "pre-commit": ["test", "typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/fastq.git"}, "scripts": {"coverage": "nyc --reporter=html --reporter=cobertura --reporter=text tape test/test.js test/promise.js", "legacy": "tape test/test.js", "lint": "standard --verbose | snazzy", "test": "npm run lint && npm run unit", "test:report": "npm run lint && npm run unit:report", "typescript": "tsc --project ./test/tsconfig.json", "unit": "nyc --lines 100 --branches 100 --functions 100 --check-coverage --reporter=text tape test/test.js test/promise.js"}, "standard": {"ignore": ["example.mjs"]}, "version": "1.19.1"}