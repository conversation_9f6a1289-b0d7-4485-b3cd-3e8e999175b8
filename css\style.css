/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
}

/* 标题样式 */
h1 {
    color: #2c3e50;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

.card-header h5 {
    margin: 0;
    font-weight: 500;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 5px;
    border-radius: 10px 10px 0 0;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border-top: none;
    font-weight: 600;
    position: relative;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.table td {
    vertical-align: middle;
}

/* 可编辑表格样式 */
.editable-table th {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    cursor: pointer;
    user-select: none;
    color: white;
}

.editable-table th:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
}

.editable-table th input {
    background: rgba(255,255,255,0.9);
    color: #333;
    border: 1px solid rgba(255,255,255,0.3);
    font-weight: 600;
}

.editable-table th input:focus {
    background: white;
    color: #333;
}

.editable-table td {
    cursor: text;
    position: relative;
}

.editable-table td:hover {
    background-color: #f8f9fa;
}

.editable-table input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 8px;
    outline: none;
}

.editable-table input:focus {
    background-color: white;
    border: 2px solid #007bff;
    border-radius: 4px;
}

/* 删除按钮样式 */
.delete-row-btn, .delete-col-btn {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    border: none;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    line-height: 1;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.editable-table tr:hover .delete-row-btn,
.editable-table th:hover .delete-col-btn {
    opacity: 1;
}

/* 进度条样式 */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(45deg, #28a745, #20c997);
    border-radius: 15px;
    transition: width 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-outline-primary {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
    color: white;
    font-weight: 600;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    border-color: #20c997;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-outline-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    color: white;
    font-weight: 600;
}

.btn-outline-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    border-color: #c82333;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-outline-success {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
    color: white;
    font-weight: 600;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    border-color: #138496;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

/* 文件输入样式 */
.form-control[type="file"] {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s ease;
}

.form-control[type="file"]:hover {
    border-color: #007bff;
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn-group .btn {
        padding: 5px 10px;
        font-size: 12px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 结果状态样式 */
.result-success {
    color: #28a745;
    font-weight: 600;
}

.result-error {
    color: #dc3545;
    font-weight: 600;
}

.result-pending {
    color: #ffc107;
    font-weight: 600;
}

/* 统计信息样式 */
.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
