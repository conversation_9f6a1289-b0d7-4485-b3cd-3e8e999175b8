{"_from": "@vue/server-renderer@3.5.17", "_id": "@vue/server-renderer@3.5.17", "_inBundle": false, "_integrity": "sha512-B<PERSON>hm8HalujY6lmC3DbqF6uXN/K00uWiEeF22LfEsm9Q93XeJ/plHTepGwf6tqFcF7GA5oGSSAAUock3VvzaCA==", "_location": "/@vue/server-renderer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/server-renderer@3.5.17", "name": "@vue/server-renderer", "escapedName": "@vue%2fserver-renderer", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.17.tgz", "_shasum": "9b8fd6a40a3d55322509fafe78ac841ede649fbe", "_spec": "@vue/server-renderer@3.5.17", "_where": "/mnt/e/www/demo1/node_modules/vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17"}, "deprecated": false, "description": "@vue/server-renderer", "exports": {".": {"types": "./dist/server-renderer.d.ts", "node": {"production": "./dist/server-renderer.cjs.prod.js", "development": "./dist/server-renderer.cjs.js", "default": "./index.js"}, "module": "./dist/server-renderer.esm-bundler.js", "import": "./dist/server-renderer.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "name": "@vue/server-renderer", "peerDependencies": {"vue": "3.5.17"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "types": "dist/server-renderer.d.ts", "version": "3.5.17"}