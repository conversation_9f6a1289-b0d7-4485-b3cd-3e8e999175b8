{"_from": "local-pkg@^0.4.3", "_id": "local-pkg@0.4.3", "_inBundle": false, "_integrity": "sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g==", "_location": "/unplugin-vue-components/local-pkg", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "local-pkg@^0.4.3", "name": "local-pkg", "escapedName": "local-pkg", "rawSpec": "^0.4.3", "saveSpec": null, "fetchSpec": "^0.4.3"}, "_requiredBy": ["/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/local-pkg/-/local-pkg-0.4.3.tgz", "_shasum": "0ff361ab3ae7f1c19113d9bb97b98b905dbc4963", "_spec": "local-pkg@^0.4.3", "_where": "/mnt/e/www/demo1/node_modules/unplugin-vue-components", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get information on local packages.", "devDependencies": {"@antfu/eslint-config": "^0.34.1", "@antfu/ni": "^0.18.8", "@antfu/utils": "^0.7.2", "@types/chai": "^4.3.4", "@types/node": "^18.11.18", "bumpp": "^8.2.1", "chai": "^4.3.7", "eslint": "^8.32.0", "esno": "^0.16.3", "find-up": "^6.3.0", "tsup": "^6.5.0", "typescript": "^4.9.4"}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}}, "files": ["dist", "index.cjs", "index.mjs", "index.d.ts"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "keywords": ["package"], "license": "MIT", "main": "index.cjs", "module": "index.mjs", "name": "local-pkg", "packageManager": "pnpm@7.5.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "scripts": {"build": "tsup shared.ts --format esm,cjs --dts && esno scripts/postbuild.ts", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp && npm publish", "test": "node test/cjs.cjs && node test/esm.mjs"}, "sideEffects": false, "types": "index.d.ts", "version": "0.4.3"}