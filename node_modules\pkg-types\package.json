{"_from": "pkg-types@^1.2.1", "_id": "pkg-types@1.3.1", "_inBundle": false, "_integrity": "sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==", "_location": "/pkg-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pkg-types@^1.2.1", "name": "pkg-types", "escapedName": "pkg-types", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/local-pkg", "/mlly", "/unimport"], "_resolved": "https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz", "_shasum": "bd7cc70881192777eef5326c19deb46e890917df", "_spec": "pkg-types@^1.2.1", "_where": "/mnt/e/www/demo1/node_modules/local-pkg", "bugs": {"url": "https://github.com/unjs/pkg-types/issues"}, "bundleDependencies": false, "dependencies": {"confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1"}, "deprecated": false, "description": "Node.js utilities and TypeScript definitions for `package.json` and `tsconfig.json`", "devDependencies": {"@types/node": "^22.10.6", "@vitest/coverage-v8": "^2.1.8", "automd": "^0.3.12", "changelogen": "^0.5.7", "eslint": "^9.18.0", "eslint-config-unjs": "^0.4.2", "expect-type": "^1.1.0", "jiti": "^2.4.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^2.1.8"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "files": ["dist"], "homepage": "https://github.com/unjs/pkg-types#readme", "license": "MIT", "main": "./dist/index.cjs", "name": "pkg-types", "packageManager": "pnpm@9.15.4", "repository": {"type": "git", "url": "git+https://github.com/unjs/pkg-types.git"}, "scripts": {"build": "unbuild", "dev": "vitest --typecheck", "lint": "eslint && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "vitest run --typecheck --coverage"}, "sideEffects": false, "types": "./dist/index.d.ts", "version": "1.3.1"}