{"_from": "estree-walker@^3.0.3", "_id": "estree-walker@3.0.3", "_inBundle": false, "_integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "_location": "/unimport/estree-walker", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "estree-walker@^3.0.3", "name": "estree-walker", "escapedName": "estree-walker", "rawSpec": "^3.0.3", "saveSpec": null, "fetchSpec": "^3.0.3"}, "_requiredBy": ["/unimport"], "_resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "_shasum": "67c3e549ec402a487b4fc193d1953a524752340d", "_spec": "estree-walker@^3.0.3", "_where": "/mnt/e/www/demo1/node_modules/unimport", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/estree-walker/issues"}, "bundleDependencies": false, "dependencies": {"@types/estree": "^1.0.0"}, "deprecated": false, "description": "Traverse an ESTree-compliant AST", "devDependencies": {"typescript": "^4.9.0", "uvu": "^0.5.1"}, "exports": {"./package.json": "./package.json", ".": {"types": "./types/index.d.ts", "import": "./src/index.js"}}, "files": ["src", "types", "README.md"], "homepage": "https://github.com/<PERSON>-<PERSON>/estree-walker#readme", "license": "MIT", "module": "./src/index.js", "name": "estree-walker", "private": false, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/estree-walker.git"}, "scripts": {"prepublishOnly": "tsc && npm test", "test": "uvu test"}, "type": "module", "types": "types/index.d.ts", "version": "3.0.3"}