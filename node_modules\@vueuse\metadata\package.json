{"_from": "@vueuse/metadata@9.13.0", "_id": "@vueuse/metadata@9.13.0", "_inBundle": false, "_integrity": "sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==", "_location": "/@vueuse/metadata", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vueuse/metadata@9.13.0", "name": "@vueuse/metadata", "escapedName": "@vueuse%2fmetadata", "scope": "@vueuse", "rawSpec": "9.13.0", "saveSpec": null, "fetchSpec": "9.13.0"}, "_requiredBy": ["/@vueuse/core"], "_resolved": "https://registry.npmjs.org/@vueuse/metadata/-/metadata-9.13.0.tgz", "_shasum": "bc25a6cdad1b1a93c36ce30191124da6520539ff", "_spec": "@vueuse/metadata@9.13.0", "_where": "/mnt/e/www/demo1/node_modules/@vueuse/core", "author": {"name": "<PERSON>", "email": "https://github.com/antfu"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Metadata for VueUse functions", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*"}, "files": ["index.*"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/metadata#readme", "keywords": ["vue", "vue-use"], "license": "MIT", "main": "./index.cjs", "module": "./index.mjs", "name": "@vueuse/metadata", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/metadata"}, "scripts": {"update": "esno scripts/update.ts"}, "sideEffects": false, "types": "./index.d.ts", "version": "9.13.0"}