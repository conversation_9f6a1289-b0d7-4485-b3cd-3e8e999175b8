{"_from": "@antfu/utils@^0.7.6", "_id": "@antfu/utils@0.7.10", "_inBundle": false, "_integrity": "sha512-+562v9k4aI80m1+VuMHehNJWLOFjBnXn3tdOitzD0il5b7smkSBal4+a3oKiQTbrwMmN/TBUMDvbdoWDehgOww==", "_location": "/@antfu/utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@antfu/utils@^0.7.6", "name": "@antfu/utils", "escapedName": "@antfu%2futils", "scope": "@antfu", "rawSpec": "^0.7.6", "saveSpec": null, "fetchSpec": "^0.7.6"}, "_requiredBy": ["/unplugin-auto-import", "/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/@antfu/utils/-/utils-0.7.10.tgz", "_shasum": "ae829f170158e297a9b6a28f161a8e487d00814d", "_spec": "@antfu/utils@^0.7.6", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/utils/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Opinionated collection of common JavaScript / TypeScript utils by @antfu", "devDependencies": {"@antfu/eslint-config": "^2.16.3", "@antfu/ni": "^0.21.12", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@types/node": "^20.12.10", "@types/throttle-debounce": "^5.0.2", "bumpp": "^9.4.1", "eslint": "npm:eslint-ts-patch@8.55.0-1", "eslint-ts-patch": "8.55.0-1", "esno": "^4.7.0", "p-limit": "^5.0.0", "rollup": "^4.17.2", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "throttle-debounce": "5.0.0", "typescript": "^5.4.5", "vite": "^5.2.11", "vitest": "^1.6.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/utils#readme", "keywords": ["utils"], "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.mjs", "name": "@antfu/utils", "packageManager": "pnpm@9.1.0", "repository": {"type": "git", "url": "git+https://github.com/antfu/utils.git"}, "scripts": {"build": "rollup -c", "dev": "nr build --watch", "lint": "eslint .", "lint-fix": "nr lint --fix", "prepublishOnly": "npm run build", "release": "bumpp --commit --push --tag && npm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "version": "0.7.10"}