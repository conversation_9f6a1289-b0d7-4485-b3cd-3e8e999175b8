{"_from": "micromatch@^4.0.8", "_id": "micromatch@4.0.8", "_inBundle": false, "_integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "_location": "/micromatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "micromatch@^4.0.8", "name": "micromatch", "escapedName": "micromatch", "rawSpec": "^4.0.8", "saveSpec": null, "fetchSpec": "^4.0.8"}, "_requiredBy": ["/fast-glob"], "_resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "_shasum": "d66fa18f3a47076789320b9b1af32bd86d9fa202", "_spec": "micromatch@^4.0.8", "_where": "/mnt/e/www/demo1/node_modules/fast-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "bundleDependencies": false, "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"name": "<PERSON><PERSON>", "url": "amilajack.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "Devon Govett", "url": "http://badassjs.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://ultcombo.js.org"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://kolarik.sk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON>", "url": "https://github.com/tomByrer"}, {"name": "<PERSON>", "url": "http://rumkin.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/drpizza"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ku8ar"}], "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "deprecated": false, "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^5.0.1", "mocha": "^9.2.2", "time-require": "github:jonschlink<PERSON>/time-require"}, "engines": {"node": ">=8.6"}, "files": ["index.js"], "homepage": "https://github.com/micromatch/micromatch", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "license": "MIT", "main": "index.js", "name": "micromatch", "repository": {"type": "git", "url": "git+https://github.com/micromatch/micromatch.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "version": "4.0.8"}