{"_from": "local-pkg@^0.5.0", "_id": "local-pkg@0.5.1", "_inBundle": false, "_integrity": "sha512-9rrA30MRRP3gBD3HTGnC6cDFpaE1kVDWxWgqWJUN0RvDNAo+Nz/9GxB+nHOH0ifbVFy0hSA1V6vFDvnx54lTEQ==", "_location": "/local-pkg", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "local-pkg@^0.5.0", "name": "local-pkg", "escapedName": "local-pkg", "rawSpec": "^0.5.0", "saveSpec": null, "fetchSpec": "^0.5.0"}, "_requiredBy": ["/unplugin-auto-import"], "_resolved": "https://registry.npmjs.org/local-pkg/-/local-pkg-0.5.1.tgz", "_shasum": "69658638d2a95287534d4c2fff757980100dbb6d", "_spec": "local-pkg@^0.5.0", "_where": "/mnt/e/www/demo1/node_modules/unplugin-auto-import", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "bundleDependencies": false, "dependencies": {"mlly": "^1.7.3", "pkg-types": "^1.2.1"}, "deprecated": false, "description": "Get information on local packages.", "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@antfu/ni": "^0.23.1", "@antfu/utils": "^0.7.10", "@types/chai": "^5.0.1", "@types/node": "^22.9.0", "bumpp": "^9.8.1", "chai": "^5.1.2", "eslint": "^9.15.0", "esno": "^4.8.0", "find-up": "^6.3.0", "typescript": "^5.6.3", "unbuild": "^2.0.0", "vitest": "^2.1.5"}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "keywords": ["package"], "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.mjs", "name": "local-pkg", "packageManager": "pnpm@9.13.2", "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "scripts": {"build": "unbuild", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp && npm publish", "test": "vitest run && node ./test/cjs.cjs && node ./test/esm.mjs", "typecheck": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "version": "0.5.1"}