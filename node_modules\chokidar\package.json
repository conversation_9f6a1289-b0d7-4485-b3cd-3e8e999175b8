{"_from": "chokidar@^3.5.3", "_id": "chokidar@3.6.0", "_inBundle": false, "_integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "_location": "/chokidar", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "chokidar@^3.5.3", "name": "chokidar", "escapedName": "chokidar", "rawSpec": "^3.5.3", "saveSpec": null, "fetchSpec": "^3.5.3"}, "_requiredBy": ["/unplugin-vue-components"], "_resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "_shasum": "197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b", "_spec": "chokidar@^3.5.3", "_where": "/mnt/e/www/demo1/node_modules/unplugin-vue-components", "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://paulmillr.com"}, {"name": "<PERSON><PERSON>"}], "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "deprecated": false, "description": "Minimal and efficient cross-platform file watching library", "devDependencies": {"@types/node": "^14", "chai": "^4.3", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "typescript": "^4.4.3", "upath": "^1.2.0"}, "engines": {"node": ">= 8.10.0"}, "files": ["index.js", "lib/*.js", "types/index.d.ts"], "funding": "https://paulmillr.com/funding/", "homepage": "https://github.com/paulmillr/chokidar", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "license": "MIT", "main": "index.js", "name": "chokidar", "optionalDependencies": {"fsevents": "~2.3.2"}, "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "scripts": {"build": "npm ls", "dtslint": "dtslint types", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --exit --timeout 90000", "test": "npm run lint && npm run mocha"}, "types": "./types/index.d.ts", "version": "3.6.0"}